// Script for 示例脚本
const fs = require('fs');
const path = require('path');

// Get configuration from command line arguments
let config = {};
if (process.argv[2]) {
  try {
    const configString = Buffer.from(process.argv[2], 'base64').toString('utf-8');
    config = JSON.parse(configString);
    console.log('--- Running script 示例脚本 ---');
    console.log('Received configuration:');
    console.log(JSON.stringify(config, null, 2));
    console.log('');
  } catch (e) {
    console.error('Failed to parse configuration:', e.message);
    process.exit(1);
  }
} else {
  console.log('--- Running script 示例脚本 ---');
  console.log('No configuration provided');
  console.log('');
}

// Your script logic here
console.log('Current time:', new Date().toLocaleString());

if (config.enable_logging) {
  console.log('Logging is enabled.');
}

if (config.run_mode) {
  console.log('Running in mode:', config.run_mode);
}

if (config.max_retries) {
  console.log('Max retries:', config.max_retries);
}

// 示例：读取当前目录的文件
try {
  const files = fs.readdirSync(__dirname);
  console.log('Files in script directory:', files);
} catch (error) {
  console.error('Error reading directory:', error.message);
}

// 示例：Node.js版本信息
console.log('Node.js version:', process.version);
console.log('Platform:', process.platform);

console.log('');
console.log('--- Script finished ---');
