# Script for 获取当前时间
import sys
import json
import base64
import time

def main(config):
    """
    Main execution function.
    'config' is a dictionary with your script's parameters.
    """
    print(f"--- Running script '获取当前时间' ---")
    print("Received configuration:")
    print(json.dumps(config, indent=2, ensure_ascii=False))
    
    # --- Your script logic starts here ---
    print(f"\nCurrent time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    # --- Your script logic ends here ---
    
    print("\n--- Script finished ---")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        try:
            # The first argument is the Base64 encoded JSON string
            config_base64 = sys.argv[1]
            config_json_bytes = base64.b64decode(config_base64)
            config_json = config_json_bytes.decode('utf-8')
            config_data = json.loads(config_json)
            main(config_data)
        except Exception as e:
            print(f"Error decoding or parsing config from Base64: {e}")
            sys.exit(1)
    else:
        # Fallback for direct execution without arguments

        print("Running with default empty config (no arguments provided).")
        main({})
