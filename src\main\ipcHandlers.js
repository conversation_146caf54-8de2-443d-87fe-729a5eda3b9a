import { ipcMain, dialog } from 'electron'
import { open, Database } from 'sqlite'
import sqlite3 from 'sqlite3'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import fs from 'fs-extra'
import { exec } from 'child_process'
import iconv from 'iconv-lite'

const __dirname = dirname(fileURLToPath(import.meta.url))
const dbPath = join(__dirname, '../../database.db')

console.log('Database path:', dbPath)

let db

async function initializeDatabase() {
  try {
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    })

    await db.exec(`
      CREATE TABLE IF NOT EXISTS cmd_scripts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        content TEXT
      );
    `)

    await db.exec(`
      CREATE TABLE IF NOT EXISTS script_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        script_id INTEGER,
        log_output TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (script_id) REFERENCES cmd_scripts (id) ON DELETE CASCADE
      );
    `)

    await db.exec(`
      CREATE TABLE IF NOT EXISTS python_script_configs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          script_name TEXT UNIQUE NOT NULL,
          config_schema TEXT,
          config_values TEXT
      );
    `)
  } catch (err) {
    console.error('Failed to initialize database:', err)
  }
}

initializeDatabase()

// CMD Scripts IPC Handlers
ipcMain.handle('db:get-cmd-scripts', async () => {
  return await db.all('SELECT * FROM cmd_scripts')
})

ipcMain.handle('db:add-cmd-script', async (event, name) => {
  const result = await db.run('INSERT INTO cmd_scripts (name, content) VALUES (?, ?)', name, '')
  return { id: result.lastID, name, content: '' }
})

ipcMain.handle('db:update-cmd-script', async (event, { id, content }) => {
  return await db.run('UPDATE cmd_scripts SET content = ? WHERE id = ?', content, id)
})

ipcMain.handle('db:delete-cmd-script', async (event, id) => {
  return await db.run('DELETE FROM cmd_scripts WHERE id = ?', id)
})

ipcMain.handle('cmd:run-script', async (event, { id, code }) => {
  const tempDir = join(__dirname, '../../temp_scripts')
  await fs.ensureDir(tempDir)
  const scriptPath = join(tempDir, `script_${Date.now()}.cmd`)

  const gbkBuffer = iconv.encode(code, 'gbk')

  await fs.writeFile(scriptPath, gbkBuffer)

  return new Promise((resolve, reject) => {
    const process = exec(`cmd.exe /c ${scriptPath}`, (error, stdout, stderr) => {
      fs.remove(scriptPath) // Clean up the temp file

      const logOutput = stdout + stderr
      if (db) {
        db.run('INSERT INTO script_logs (script_id, log_output) VALUES (?, ?)', id, logOutput)
      }

      if (error) {
        reject({
          message: error.message,
          stdout: iconv.decode(Buffer.from(stdout, 'binary'), 'gbk'),
          stderr: iconv.decode(Buffer.from(stderr, 'binary'), 'gbk')
        })
      } else {
        resolve({
          stdout: iconv.decode(Buffer.from(stdout, 'binary'), 'gbk'),
          stderr: iconv.decode(Buffer.from(stderr, 'binary'), 'gbk')
        })
      }
    })
  })
})

ipcMain.handle('db:get-script-logs', async (event, scriptId) => {
  if (!db) {
    return []
  }
  return await db.all('SELECT * FROM script_logs WHERE script_id = ? ORDER BY timestamp DESC', scriptId)
})

// Python Scripts IPC Handlers
const scriptsRoot = join(__dirname, '../../scripts')
const pythonScriptsPath = join(scriptsRoot, 'python')

fs.ensureDirSync(pythonScriptsPath)

ipcMain.handle('python:get-scripts', async () => {
  try {
    const entries = await fs.readdir(pythonScriptsPath, { withFileTypes: true })
    const directories = entries.filter((dirent) => dirent.isDirectory()).map((dirent) => dirent.name)
    return directories
  } catch (error) {
    console.error('Failed to get Python scripts:', error)
    return []
  }
})

ipcMain.handle('python:add-script', async (event, name) => {
  const scriptDir = join(pythonScriptsPath, name)
  if (await fs.pathExists(scriptDir)) {
    throw new Error('Script with this name already exists.')
  }
  await fs.ensureDir(scriptDir)

  const defaultConfig = {
    schema: [
      { name: 'param1', label: '参数一', type: 'text', default: 'default_value' },
      { name: 'param2', label: '参数二', type: 'number', default: 100 }
    ]
  }
  const configPath = join(scriptDir, 'config.json')
  await fs.writeJson(configPath, defaultConfig, { spaces: 2 })

  const mainPyPath = join(scriptDir, 'main.py')
  const mainPyContent = `import sys
import json
import base64

def main():
    try:
        if len(sys.argv) > 1:
            # Decode the base64 string
            base64_config = sys.argv[1]
            decoded_config_str = base64.b64decode(base64_config).decode('utf-8')
            config = json.loads(decoded_config_str)
            print(f"Python script received config: {config}")
            
            # Your script logic here
            # For example, print one of the parameters
            print(f"Value of param1: {config.get('param1')}")
            
        else:
            print("No config provided.")
            
    except Exception as e:
        print(f"Error in python script: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
`
  await fs.writeFile(mainPyPath, mainPyContent)

  try {
    await db.run(
      'INSERT INTO python_script_configs (script_name, config_schema, config_values) VALUES (?, ?, ?)',
      name,
      JSON.stringify(defaultConfig.schema),
      JSON.stringify({})
    )
  } catch (dbError) {
    // If there's a DB error, we should roll back the file system changes
    await fs.remove(scriptDir)
    throw dbError
  }

  return { name }
})

ipcMain.handle('python:delete-script', async (event, name) => {
  const scriptDir = join(pythonScriptsPath, name)
  await fs.remove(scriptDir)
  await db.run('DELETE FROM python_script_configs WHERE script_name = ?', name)
  return { success: true }
})

ipcMain.handle('python:get-script-config', async (event, scriptName) => {
  const scriptDir = join(pythonScriptsPath, scriptName)
  const configPath = join(scriptDir, 'config.json')

  let dbConfig = await db.get('SELECT * FROM python_script_configs WHERE script_name = ?', scriptName)

  if (!dbConfig && (await fs.pathExists(configPath))) {
    const fileConfig = await fs.readJson(configPath)
    const schema = JSON.stringify(fileConfig.schema || [])
    const values = JSON.stringify({})

    await db.run(
      'INSERT INTO python_script_configs (script_name, config_schema, config_values) VALUES (?, ?, ?)',
      scriptName,
      schema,
      values
    )
    dbConfig = { script_name: scriptName, config_schema: schema, config_values: values }
  }

  if (dbConfig) {
    return {
      schema: JSON.parse(dbConfig.config_schema || '[]'),
      values: JSON.parse(dbConfig.config_values || '{}')
    }
  }

  return null
})

ipcMain.handle('python:save-script-config', async (event, scriptName, configValues) => {
  await db.run('UPDATE python_script_configs SET config_values = ? WHERE script_name = ?', JSON.stringify(configValues), scriptName)
  return { success: true }
})

ipcMain.handle('python:run-script', async (event, scriptName) => {
  const configRow = await db.get('SELECT config_values FROM python_script_configs WHERE script_name = ?', scriptName)
  if (!configRow) {
    throw new Error('Configuration not found for this script.')
  }
  
  const configValues = JSON.parse(configRow.config_values || '{}')
  
  // Base64 encode the config to pass as a single, safe argument
  const configJson = JSON.stringify(configValues)
  const base64Config = Buffer.from(configJson).toString('base64')
  
  const scriptPath = join(pythonScriptsPath, scriptName, 'main.py')
  const command = `python "${scriptPath}" "${base64Config}"`

  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject({
          name: 'PythonExecutionError',
          message: `Error executing Python script: ${error.message}`,
          stdout,
          stderr
        });
      } else {
        resolve({ stdout, stderr });
      }
    });
  });
}); 