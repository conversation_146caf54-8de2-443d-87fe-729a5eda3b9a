<script setup>
import { ref, markRaw, onMounted, computed } from 'vue'
import SidebarMenu from './components/SidebarMenu.vue'
import CmdScripts from './components/CmdScripts.vue'
import JsScripts from './components/JsScripts.vue'
import PythonScripts from './components/PythonScripts.vue'
import Workbench from './components/Workbench.vue'
import HotkeySettings from './components/HotkeySettings.vue'
import Settings from './components/Settings.vue'
import ScheduledTasks from './components/ScheduledTasks.vue'

const components = {
  CmdScripts: markRaw(CmdScripts),
  JsScripts: markRaw(JsScripts),
  PythonScripts: markRaw(PythonScripts),
  Workbench: markRaw(Workbench),
  ScheduledTasks: markRaw(ScheduledTasks),
  Hotkeys: markRaw(HotkeySettings),
  Settings: markRaw(Settings)
}

const activeComponentName = ref('Workbench')

onMounted(() => {
  const savedComponent = sessionStorage.getItem('activeMenu')
  if (savedComponent && components[savedComponent]) {
    activeComponentName.value = savedComponent
  }
})

const activeComponent = computed(() => {
  return components[activeComponentName.value]
})

function handleMenuClick(componentName) {
  if (components[componentName]) {
    activeComponentName.value = componentName
    sessionStorage.setItem('activeMenu', componentName)
  }
}
</script>

<template>
  <div class="app-container">
    <SidebarMenu @menu-click="handleMenuClick" :active-menu="activeComponentName" />
    <main class="main-content">
      <component :is="activeComponent" />
    </main>
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: #1a1a1a;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow: hidden;
  padding: 0;
  margin: 0;
  background-color: #1a1a1a;
}
</style>
