<template>
  <div class="execution-log-container">
    <div class="log-header">
      <h3>执行日志</h3>
      <div class="log-controls">
        <div class="search-section">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="搜索日志内容..."
            class="search-input"
          />
          <button @click="clearSearch" class="clear-search-btn" v-if="searchQuery">✕</button>
        </div>
        <div class="filter-section">
          <select v-model="statusFilter" class="status-filter">
            <option value="">全部状态</option>
            <option value="success">成功</option>
            <option value="error">失败</option>
            <option value="running">运行中</option>
          </select>
          <select v-model="dateFilter" class="date-filter">
            <option value="">全部时间</option>
            <option value="today">今天</option>
            <option value="yesterday">昨天</option>
            <option value="week">最近一周</option>
            <option value="month">最近一月</option>
          </select>
        </div>
        <div class="action-section">
          <button @click="refreshLogs" class="refresh-btn" :disabled="loading">
            <span v-if="loading" class="loading-spinner">⟳</span>
            <span v-else>🔄</span>
            刷新
          </button>
          <button @click="exportLogs" class="export-btn">
            📥 导出日志
          </button>
          <button @click="clearLogs" class="clear-btn">
            🗑️ 清空日志
          </button>
        </div>
      </div>
    </div>

    <div class="log-content">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">⟳</div>
        <p>正在加载日志...</p>
      </div>

      <div v-else-if="filteredLogs.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <p v-if="logs.length === 0">暂无执行日志</p>
        <p v-else>没有找到匹配的日志</p>
        <span v-if="logs.length === 0">任务执行后将在此显示日志</span>
        <span v-else>尝试调整搜索条件或筛选器</span>
      </div>

      <div v-else class="log-list">
        <div
          v-for="log in filteredLogs"
          :key="log.id"
          class="log-entry"
          :class="log.status"
        >
          <div class="log-header-info">
            <div class="log-meta">
              <span class="log-timestamp">{{ formatTime(log.timestamp) }}</span>
              <span class="log-task-name">{{ log.taskName }}</span>
              <span class="log-status" :class="log.status">
                {{ getStatusText(log.status) }}
              </span>
            </div>
            <button 
              @click="toggleLogExpanded(log.id)" 
              class="expand-btn"
              :class="{ expanded: expandedLogs.includes(log.id) }"
            >
              {{ expandedLogs.includes(log.id) ? '▼' : '▶' }}
            </button>
          </div>
          
          <div v-if="expandedLogs.includes(log.id)" class="log-details">
            <div v-if="log.output" class="log-output">
              <h5>输出内容:</h5>
              <pre class="log-text">{{ log.output }}</pre>
            </div>
            
            <div v-if="log.error" class="log-error">
              <h5>错误信息:</h5>
              <pre class="log-text error">{{ log.error }}</pre>
            </div>
            
            <div class="log-metadata">
              <div class="metadata-item">
                <span class="metadata-label">执行时长:</span>
                <span class="metadata-value">{{ log.duration || '未知' }}</span>
              </div>
              <div class="metadata-item" v-if="log.exitCode !== undefined">
                <span class="metadata-label">退出代码:</span>
                <span class="metadata-value">{{ log.exitCode }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="log-footer">
      <div class="log-stats">
        <span>共 {{ logs.length }} 条日志</span>
        <span v-if="searchQuery || statusFilter">，显示 {{ filteredLogs.length }} 条</span>
      </div>
      <div class="pagination" v-if="totalPages > 1">
        <button 
          @click="currentPage--" 
          :disabled="currentPage <= 1"
          class="page-btn"
        >
          上一页
        </button>
        <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
        <button 
          @click="currentPage++" 
          :disabled="currentPage >= totalPages"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'

// Props
const props = defineProps({
  taskId: {
    type: [String, Number],
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close'])

// 响应式数据
const logs = ref([])
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const dateFilter = ref('')
const expandedLogs = ref([])
const currentPage = ref(1)
const pageSize = 50

// 计算属性
const filteredLogs = computed(() => {
  let filtered = logs.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(log =>
      log.taskName.toLowerCase().includes(query) ||
      (log.output && log.output.toLowerCase().includes(query)) ||
      (log.error && log.error.toLowerCase().includes(query))
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(log => log.status === statusFilter.value)
  }

  // 日期过滤
  if (dateFilter.value) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    filtered = filtered.filter(log => {
      const logDate = new Date(log.timestamp)

      switch (dateFilter.value) {
        case 'today':
          return logDate >= today
        case 'yesterday':
          const yesterday = new Date(today)
          yesterday.setDate(yesterday.getDate() - 1)
          return logDate >= yesterday && logDate < today
        case 'week':
          const weekAgo = new Date(today)
          weekAgo.setDate(weekAgo.getDate() - 7)
          return logDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today)
          monthAgo.setMonth(monthAgo.getMonth() - 1)
          return logDate >= monthAgo
        default:
          return true
      }
    })
  }

  // 分页
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filtered.slice(start, end)
})

const totalPages = computed(() => {
  const total = logs.value.length
  return Math.ceil(total / pageSize)
})

// 方法
async function loadLogs() {
  if (!props.taskId) return

  loading.value = true
  try {
    const result = await window.api.getScheduledTaskLogs(props.taskId, {
      limit: 1000,
      offset: 0
    })

    if (result.success) {
      logs.value = result.logs.map(log => ({
        id: log.id,
        taskName: log.task_name,
        timestamp: log.start_time,
        status: log.status,
        output: log.output,
        error: log.error_message,
        duration: log.duration_ms ? `${(log.duration_ms / 1000).toFixed(1)}秒` : null,
        exitCode: log.exit_code
      }))
    } else {
      console.error('Failed to load logs:', result.error)
      logs.value = []
    }
  } catch (error) {
    console.error('Failed to load logs:', error)
    logs.value = []
  } finally {
    loading.value = false
  }
}

function generateMockLogs() {
  // 生成模拟日志数据用于演示
  const mockLogs = []
  const statuses = ['success', 'error', 'running']
  const taskNames = ['数据备份任务', '系统清理任务', 'API监控任务']
  
  for (let i = 0; i < 25; i++) {
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    mockLogs.push({
      id: i + 1,
      taskName: taskNames[Math.floor(Math.random() * taskNames.length)],
      timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      status: status,
      output: status === 'success' ? '任务执行成功\n处理了 150 个文件\n总耗时: 2.5秒' : 
              status === 'error' ? '任务执行失败\n错误: 文件权限不足' : '任务正在执行中...',
      error: status === 'error' ? 'Permission denied: /system/backup/' : null,
      duration: status !== 'running' ? `${(Math.random() * 10).toFixed(1)}秒` : null,
      exitCode: status === 'success' ? 0 : status === 'error' ? 1 : undefined
    })
  }
  
  return mockLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
}

function refreshLogs() {
  loadLogs()
}

async function clearLogs() {
  if (confirm('确定要清空所有执行日志吗？此操作不可撤销！')) {
    try {
      const result = await window.api.clearScheduledTaskLogs(props.taskId)
      if (result.success) {
        logs.value = []
        console.log(`已清空 ${result.deletedCount} 条日志`)
      } else {
        console.error('Failed to clear logs:', result.error)
        alert('清空日志失败: ' + result.error)
      }
    } catch (error) {
      console.error('Failed to clear logs:', error)
      alert('清空日志失败: ' + error.message)
    }
  }
}

function clearSearch() {
  searchQuery.value = ''
}

function exportLogs() {
  try {
    const exportData = logs.value.map(log => ({
      时间: formatTime(log.timestamp),
      任务名称: log.taskName,
      状态: getStatusText(log.status),
      执行时长: log.duration || '未知',
      输出: log.output || '',
      错误: log.error || '',
      退出代码: log.exitCode !== undefined ? log.exitCode : ''
    }))

    const csvContent = convertToCSV(exportData)
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `execution_logs_${new Date().toISOString().slice(0, 10)}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  } catch (error) {
    console.error('Failed to export logs:', error)
    alert('导出日志失败: ' + error.message)
  }
}

function convertToCSV(data) {
  if (!data.length) return ''

  const headers = Object.keys(data[0])
  const csvHeaders = headers.join(',')

  const csvRows = data.map(row =>
    headers.map(header => {
      const value = row[header] || ''
      // 转义CSV中的特殊字符
      return `"${value.toString().replace(/"/g, '""')}"`
    }).join(',')
  )

  return [csvHeaders, ...csvRows].join('\n')
}

function toggleLogExpanded(logId) {
  const index = expandedLogs.value.indexOf(logId)
  if (index > -1) {
    expandedLogs.value.splice(index, 1)
  } else {
    expandedLogs.value.push(logId)
  }
}

function getStatusText(status) {
  const statusMap = {
    success: '成功',
    error: '失败',
    running: '运行中'
  }
  return statusMap[status] || status
}

function formatTime(timeString) {
  if (!timeString) return '未知时间'
  return new Date(timeString).toLocaleString('zh-CN')
}

// 监听器
watch(() => props.taskId, () => {
  if (props.taskId) {
    loadLogs()
  }
})

watch(() => props.visible, (visible) => {
  if (visible && props.taskId) {
    loadLogs()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible && props.taskId) {
    loadLogs()
  }
})
</script>

<style scoped>
.execution-log-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #1a1a1a;
  color: #e0e0e0;
  border-radius: 8px;
  border: 1px solid #333;
  overflow: hidden;
}

.log-header {
  padding: 16px;
  border-bottom: 1px solid #333;
  background-color: #1f1f1f;
}

.log-header h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #ffffff;
  font-weight: 600;
}

.log-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.search-section {
  position: relative;
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 6px;
  color: #e0e0e0;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #4a90e2;
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.clear-search-btn:hover {
  color: #e0e0e0;
}

.filter-section {
  flex-shrink: 0;
  display: flex;
  gap: 8px;
}

.status-filter, .date-filter {
  padding: 8px 12px;
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 6px;
  color: #e0e0e0;
  font-size: 14px;
  cursor: pointer;
  min-width: 100px;
}

.action-section {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.refresh-btn, .export-btn, .clear-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.refresh-btn {
  background-color: #4a90e2;
  color: white;
}

.refresh-btn:hover:not(:disabled) {
  background-color: #357abd;
}

.refresh-btn:disabled {
  background-color: #444;
  color: #888;
  cursor: not-allowed;
}

.export-btn {
  background-color: #4caf50;
  color: white;
}

.export-btn:hover {
  background-color: #45a049;
}

.clear-btn {
  background-color: #f44336;
  color: white;
}

.clear-btn:hover {
  background-color: #d32f2f;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.log-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: #888;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 8px;
  color: #b0b0b0;
}

.empty-state span {
  font-size: 14px;
  color: #888;
}

.log-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-entry {
  background-color: #252525;
  border: 1px solid #333;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.log-entry:hover {
  border-color: #444;
}

.log-entry.success {
  border-left: 4px solid #4caf50;
}

.log-entry.error {
  border-left: 4px solid #f44336;
}

.log-entry.running {
  border-left: 4px solid #ff9800;
}

.log-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
}

.log-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.log-timestamp {
  font-size: 12px;
  color: #888;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  min-width: 140px;
}

.log-task-name {
  font-size: 14px;
  color: #e0e0e0;
  font-weight: 500;
}

.log-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.log-status.success {
  background-color: #4caf50;
  color: white;
}

.log-status.error {
  background-color: #f44336;
  color: white;
}

.log-status.running {
  background-color: #ff9800;
  color: white;
}

.expand-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: #888;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  background-color: #3a3a3a;
  color: #e0e0e0;
}

.expand-btn.expanded {
  color: #4a90e2;
}

.log-details {
  padding: 0 16px 16px 16px;
  border-top: 1px solid #333;
  background-color: #1f1f1f;
}

.log-output, .log-error {
  margin-bottom: 16px;
}

.log-output h5, .log-error h5 {
  margin: 12px 0 8px 0;
  font-size: 13px;
  color: #b0b0b0;
  font-weight: 500;
}

.log-text {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 6px;
  padding: 12px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  color: #e0e0e0;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
}

.log-text.error {
  color: #ff6b6b;
  border-color: #f44336;
}

.log-metadata {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.metadata-item {
  display: flex;
  gap: 8px;
}

.metadata-label {
  font-size: 12px;
  color: #888;
  font-weight: 500;
}

.metadata-value {
  font-size: 12px;
  color: #e0e0e0;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.log-footer {
  padding: 12px 16px;
  border-top: 1px solid #333;
  background-color: #1f1f1f;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-stats {
  font-size: 12px;
  color: #888;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #444;
  border-radius: 4px;
  background-color: #2a2a2a;
  color: #e0e0e0;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  border-color: #4a90e2;
  background-color: #333;
}

.page-btn:disabled {
  background-color: #1a1a1a;
  color: #666;
  cursor: not-allowed;
}

.page-info {
  font-size: 12px;
  color: #888;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* 滚动条样式 */
.log-content::-webkit-scrollbar,
.log-text::-webkit-scrollbar {
  width: 6px;
}

.log-content::-webkit-scrollbar-track,
.log-text::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.log-content::-webkit-scrollbar-thumb,
.log-text::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 3px;
}

.log-content::-webkit-scrollbar-thumb:hover,
.log-text::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
