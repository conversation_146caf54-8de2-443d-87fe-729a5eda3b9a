<template>
  <div class="hotkey-settings">
    <div class="header">
      <h2>快捷键设置</h2>
      <p>全局热键：只支持组合键和功能键，软件启动直接生效。键鼠监听：支持任意键盘按键、鼠标侧键，功能启动后才生效（推荐）。点击类型按钮可以切换快捷键类型。其中存在长按需求的功能不能使用全局热键。</p>
    </div>
    <div class="settings-header">
      <span class="col-func">功能</span>
      <span class="col-type">快捷键类型</span>
      <span class="col-key">配置快捷键</span>
    </div>
    <div class="settings-list">
      <div v-for="item in hotkeys" :key="item.name" class="setting-item-wrapper">
        <div v-if="item.isSection" class="section-header">
          <span>{{ item.name }}</span>
        </div>
        <div v-else class="setting-item">
          <span class="col-func">{{ item.name }}</span>
          <span class="col-type"><button>{{ item.type }}</button></span>
          <span class="col-key"><button>{{ item.key }}</button></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const hotkeys = ref([
  { name: '启动/停止 BetterGI', type: '全局热键', key: 'F11' },
  { name: '系统控制', isSection: true },
  { name: '停止当前脚本/独立任务', type: '键鼠监听', key: '< None >' },
  { name: '暂停当前脚本/独立任务', type: '键鼠监听', key: '< None >' },
  { name: '游戏截图', type: '键鼠监听', key: '< None >' },
  { name: '日志与状态窗口展示开关', type: '键鼠监听', key: '< None >' },
  { name: '实时任务', isSection: true },
  { name: '自动拾取开关', type: '键鼠监听', key: '< None >' },
  { name: '自动剧情开关', type: '键鼠监听', key: '< None >' },
  { name: '自动邀约开关', type: '键鼠监听', key: '< None >' },
  { name: '自动钓鱼开关', type: '键鼠监听', key: '< None >' },
]);
</script>

<style scoped>
.hotkey-settings {
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.9); /* 提高对比度 */
  padding: 24px;
  box-sizing: border-box;
  overflow-y: auto;
  letter-spacing: 0.2px;
}

.header h2 {
  font-size: 18px; /* 增大标题 */
  font-weight: 500;
  margin-bottom: 10px;
  color: #ffffff;
  letter-spacing: 0.3px;
}

.header p {
  font-size: 14px; /* 增大说明文本 */
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7); /* 提高对比度 */
  margin-bottom: 24px;
  max-width: 800px;
}

.settings-header, .setting-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px; /* 增大表头和项目文本 */
}

.settings-header {
  color: rgba(255, 255, 255, 0.7); /* 提高对比度 */
  font-weight: 500;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* 提高边框对比度 */
  background-color: #1a1a1a;
  border-radius: 8px 8px 0 0;
  margin-bottom: 1px;
}

.setting-item {
  background-color: #1a1a1a;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08); /* 提高边框对比度 */
  font-weight: 450;
}

.setting-item:last-child {
  border-radius: 0 0 8px 8px;
  border-bottom: none;
}

.col-func { flex: 4; }
.col-type { flex: 2; text-align: center; }
.col-key { flex: 2; text-align: center; }

.col-type button, .col-key button {
  background-color: #2c2c2c;
  border: 1px solid #3a3a3a;
  color: rgba(255, 255, 255, 0.9); /* 提高按钮文字对比度 */
  padding: 5px 12px; /* 增加按钮内边距 */
  border-radius: 4px;
  font-size: 13.5px; /* 增大按钮字体 */
  cursor: pointer;
  width: 110px;
  text-align: center;
  transition: all 0.2s;
  font-weight: 450;
  letter-spacing: 0.2px;
}

.col-type button:hover, .col-key button:hover {
  background-color: #3a3a3a;
  border-color: #4a4a4a;
}

.section-header {
  padding: 20px 0 8px 16px;
  color: rgba(255, 255, 255, 0.9); /* 提高对比度 */
  font-size: 14.5px; /* 增大分组标题 */
  font-weight: 500;
  letter-spacing: 0.3px;
}

.section-header span::before {
  content: '▾';
  margin-right: 8px;
  color: rgba(255, 255, 255, 0.7); /* 提高对比度 */
}

.setting-item-wrapper:not(:first-child) .section-header {
  margin-top: 16px;
}
</style> 