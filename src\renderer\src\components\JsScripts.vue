<template>
  <div class="js-scripts-container">
    <!-- Left Panel: Script List -->
    <div class="script-list-panel">
      <div class="panel-header">
        <h3>JS 脚本</h3>
        <button @click="openAddModal" class="add-btn">新增脚本</button>
      </div>
      <ul>
        <li
          v-for="script in scripts"
          :key="script.name"
          @click="selectScript(script)"
          :class="{ active: selectedScript && selectedScript.name === script.name }"
        >
          <span>{{ script.name }}</span>
          <button @click.stop="deleteScript(script)" class="delete-btn">删除</button>
        </li>
      </ul>
    </div>

    <!-- Right Panel: Dynamic Config Form & Log Output -->
    <div class="right-panel">
      <div class="script-config-panel" v-if="selectedScript">
        <div class="config-header">
          <input type="text" :value="selectedScript.name" class="title-input" readonly />
          <div class="button-group">
            <button @click="runScript" class="run-btn" :disabled="isRunning">
              {{ isRunning ? '运行中...' : '运行' }}
            </button>
            <button @click="saveConfig" :disabled="!isDirty || isRunning" class="save-btn">
              {{ isDirty ? '保存' : '已保存' }}
            </button>
            <button @click="testNodeEnvironment" class="test-btn" :disabled="isRunning">
              测试Node环境
            </button>
          </div>
        </div>

        <div class="config-section">
          <div class="config-header">
            <h4>配置参数</h4>
            <div class="config-actions">
              <button
                @click="openAddConfigModal"
                class="add-config-btn"
                :disabled="isRunning"
                ref="addConfigBtn"
              >
                ➕ 添加配置项
              </button>
            </div>
          </div>

          <div class="config-form" v-if="config.schema">
            <div v-for="(field, index) in config.schema" :key="field.name" class="form-group">
              <div class="form-group-header">
                <label :for="field.name">{{ field.label }}</label>
                <button
                  @click="promptDeleteConfigField(index)"
                  class="remove-field-btn"
                  :disabled="isRunning"
                >
                  🗑️
                </button>
              </div>

              <input
                v-if="field.type === 'text' || field.type === 'number'"
                :type="field.type"
                :id="field.name"
                v-model="config.values[field.name]"
              />

              <select
                v-else-if="field.type === 'select'"
                :id="field.name"
                v-model="config.values[field.name]"
              >
                <option v-for="option in field.options" :key="option" :value="option">
                  {{ option }}
                </option>
              </select>

              <input
                v-else-if="field.type === 'checkbox'"
                type="checkbox"
                :id="field.name"
                v-model="config.values[field.name]"
                class="form-checkbox"
              />
            </div>

            <div v-if="config.schema.length === 0" class="no-config">
              <p>此脚本没有可配置的选项。</p>
              <p>点击"添加配置项"按钮来创建配置参数。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Log Output -->
      <div class="log-output-panel" v-if="selectedScript">
        <div class="log-header">
          <h4>运行日志</h4>
          <button @click="clearLog" class="clear-log-btn" :disabled="isRunning">清空日志</button>
        </div>
        <pre class="log-content" ref="logContentRef">{{ logOutput }}</pre>
      </div>

      <!-- Placeholder for when no script is selected -->
      <div v-if="!selectedScript" class="placeholder">
        <p>请从左侧选择一个脚本，或新增一个脚本。</p>
      </div>
    </div>

    <!-- Add Script Modal -->
    <div v-if="showAddModal" class="modal-overlay" @click.self="closeAddModal">
      <div class="modal-content">
        <h3>新增 JS 脚本</h3>
        <input
          type="text"
          v-model="newScriptName"
          placeholder="脚本名称 (将作为目录名)"
          ref="newScriptNameInput"
          @keyup.enter="addScript"
          @keyup.esc="closeAddModal"
        />
        <div class="modal-actions">
          <button @click="closeAddModal">取消</button>
          <button @click="addScript">确认创建</button>
        </div>
      </div>
    </div>

    <!-- Add Config Field Modal -->
    <div v-if="showAddConfigModal" class="modal-overlay" @click.self="closeAddConfigModal">
      <div class="modal-content config-modal">
        <h3>添加配置项</h3>
        <div class="config-form-modal">
          <div class="form-group">
            <label for="configName">配置名称 (英文)</label>
            <input
              type="text"
              id="configName"
              v-model="newConfigField.name"
              placeholder="例如: max_timeout"
              ref="newConfigNameInput"
              @keyup.enter="addConfigField"
              @keyup.esc="closeAddConfigModal"
              :key="configModalKey"
            />
          </div>

          <div class="form-group">
            <label for="configLabel">显示标签</label>
            <input
              type="text"
              id="configLabel"
              v-model="newConfigField.label"
              placeholder="例如: 最大超时时间"
            />
          </div>

          <div class="form-group">
            <label for="configType">配置类型</label>
            <select id="configType" v-model="newConfigField.type">
              <option value="text">文本</option>
              <option value="number">数字</option>
              <option value="checkbox">复选框</option>
              <option value="select">下拉选择</option>
            </select>
          </div>

          <div v-if="newConfigField.type === 'select'" class="form-group">
            <label for="configOptions">选项 (每行一个)</label>
            <textarea
              id="configOptions"
              v-model="newConfigField.optionsText"
              placeholder="选项1&#10;选项2&#10;选项3"
              rows="4"
            ></textarea>
          </div>

          <div class="form-group">
            <label for="configDefault">默认值</label>
            <input
              v-if="newConfigField.type === 'text'"
              type="text"
              id="configDefault"
              v-model="newConfigField.default"
              placeholder="默认文本值"
            />
            <input
              v-else-if="newConfigField.type === 'number'"
              type="number"
              id="configDefault"
              v-model="newConfigField.default"
              placeholder="默认数字值"
            />
            <input
              v-else-if="newConfigField.type === 'checkbox'"
              type="checkbox"
              id="configDefault"
              v-model="newConfigField.default"
            />
            <select
              v-else-if="newConfigField.type === 'select'"
              id="configDefault"
              v-model="newConfigField.default"
            >
              <option value="">请选择默认值</option>
              <option v-for="option in newConfigField.optionsText.split('\n').filter(o => o.trim())" :key="option" :value="option.trim()">
                {{ option.trim() }}
              </option>
            </select>
          </div>
        </div>

        <div class="modal-actions">
          <button @click="closeAddConfigModal">取消</button>
          <button @click="addConfigField" :disabled="!newConfigField.name || !newConfigField.label">确认添加</button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteConfirmModal" class="modal-overlay" @click.self="cancelDeleteConfigField">
      <div class="modal-content">
        <h3>确认删除</h3>
        <p v-if="configItemToDelete">
          您确定要删除配置项 "<strong>{{ configItemToDelete.field.label }}</strong>" 吗？<br />
          此操作不可恢复。
        </p>
        <div class="modal-actions">
          <button @click="cancelDeleteConfigField">取消</button>
          <button @click="confirmDeleteConfigField" class="danger-btn">确认删除</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'

const scripts = ref([])
const selectedScript = ref(null)
const config = ref({ schema: [], values: {} })
const originalValuesStr = ref('{}')
const isDirty = ref(false)
const logOutput = ref('💡 欢迎使用JS脚本管理器\n📝 请选择一个脚本开始使用。')
const isRunning = ref(false)
const logContentRef = ref(null)

const showAddModal = ref(false)
const newScriptName = ref('')
const newScriptNameInput = ref(null)

// 配置项管理相关
const showAddConfigModal = ref(false)
const newConfigNameInput = ref(null)
const addConfigBtn = ref(null)
const configModalKey = ref(0) // 用于强制重新创建输入框
const newConfigField = ref({
  name: '',
  label: '',
  type: 'text',
  default: '',
  optionsText: ''
})

// Delete confirmation modal state
const showDeleteConfirmModal = ref(false)
const configItemToDelete = ref(null) // Will store { index, field }

// 辅助函数：格式化日志输出，处理换行符和时间戳
function formatLogOutput(message, addTimestamp = true) {
  const timestamp = addTimestamp ? `[${new Date().toLocaleTimeString()}] ` : ''
  return timestamp + message.replace(/\\n/g, '\n')
}

// 辅助函数：添加日志
function addLog(message, addTimestamp = true) {
  logOutput.value += formatLogOutput(message, addTimestamp)
  // Auto-scroll to bottom
  nextTick(() => {
    if (logContentRef.value) {
      logContentRef.value.scrollTop = logContentRef.value.scrollHeight
    }
  })
}

// 辅助函数：设置日志（清空并设置新内容）
function setLog(message, addTimestamp = true) {
  logOutput.value = formatLogOutput(message, addTimestamp)
}

// 监听模态框显示状态，确保焦点设置
watch(showAddModal, async (newValue) => {
  if (newValue) {
    // 模态框显示时，确保焦点设置
    await nextTick()
    setTimeout(() => {
      newScriptNameInput.value?.focus()
    }, 150) // 稍微增加延迟确保模态框动画完成
  }
})

// 监听配置项模态框显示状态，确保焦点设置
watch(showAddConfigModal, async (newValue) => {
  if (newValue) {
    await nextTick()
    setTimeout(() => {
      newConfigNameInput.value?.focus()
    }, 150)
  }
})

async function fetchScripts() {
  scripts.value = await window.api.getJsScripts()
  if (selectedScript.value) {
    const stillExists = scripts.value.some((s) => s.name === selectedScript.value.name)
    if (!stillExists) {
      selectedScript.value = null
      config.value = { schema: [], values: {} }
      logOutput.value = '📝 请选择一个脚本。'
    }
  }
}

onMounted(async () => {
  await fetchScripts()
  if (scripts.value.length > 0) {
    selectScript(scripts.value[0])
  }
})

watch(
  () => config.value.values,
  (newValues) => {
    if (newValues && selectedScript.value) {
      isDirty.value = JSON.stringify(newValues) !== originalValuesStr.value
    }
  },
  { deep: true }
)

async function selectScript(script) {
  if (isDirty.value) {
    if (confirm('当前配置有未保存的更改，确定要切换吗？更改将丢失。')) {
      isDirty.value = false
    } else {
      return
    }
  }
  selectedScript.value = script
  logOutput.value = `✅ 已选择脚本: ${script.name}\n🚀 准备就绪，可以运行脚本。`
  const newConfig = await window.api.getJsScriptConfig(script.name)
  if (newConfig) {
    config.value = newConfig
    originalValuesStr.value = JSON.stringify(newConfig.values || {})
  } else {
    config.value = { schema: [], values: {} }
    originalValuesStr.value = '{}'
  }
  isDirty.value = false
}

async function openAddModal() {
  showAddModal.value = true
  newScriptName.value = ''
  await nextTick()
  setTimeout(() => {
    newScriptNameInput.value?.focus()
  }, 100)
}

function closeAddModal() {
  showAddModal.value = false
  newScriptName.value = ''
}

async function addScript() {
  const name = newScriptName.value.trim()
  if (!name) {
    return alert('脚本名称不能为空')
  }
  if (/[\\\\/:*?"<>|]/.test(name)) {
    return alert('脚本名称包含非法字符。')
  }

  try {
    const newScript = await window.api.addJsScript(name)
    await fetchScripts()
    const scriptInList = scripts.value.find((s) => s.name === newScript.name)
    if (scriptInList) {
      selectScript(scriptInList)
    }
    closeAddModal()
  } catch (error) {
    console.error('添加脚本时出错:', error)
    alert(`添加脚本时出错: ${error.message}`)
  }
}

async function deleteScript(script) {
  if (confirm(`确定要删除脚本目录 “${script.name}” 吗？此操作不可恢复。`)) {
    try {
      await window.api.deleteJsScript(script.name)
      if (selectedScript.value && selectedScript.value.name === script.name) {
        selectedScript.value = null
        config.value = { schema: [], values: {} }
      }
      await fetchScripts()
      if (!selectedScript.value && scripts.value.length > 0) {
        selectScript(scripts.value[0])
      }
    } catch (error) {
      console.error('删除脚本时出错:', error)
      alert(`删除脚本时出错: ${error.message}`)
    }
  }
}

async function saveConfig() {
  if (!selectedScript.value || !isDirty.value) return
  try {
    const cleanParams = {
      scriptName: String(selectedScript.value.name),
      values: JSON.parse(JSON.stringify(config.value.values || {})),
      schema: JSON.parse(JSON.stringify(config.value.schema || []))
    }
    await window.api.updateJsScriptConfig(cleanParams)
    originalValuesStr.value = JSON.stringify(config.value.values)
    isDirty.value = false
    return true
  } catch (error) {
    console.error('保存配置时出错:', error)
    alert(`保存配置时出错: ${error.message}`)
    return false
  }
}

async function runScript() {
  if (!selectedScript.value || !config.value.values) return

  addLog(`\n\n- - - - - - - - [ ${new Date().toLocaleString()} ] - - - - - - - -\n`, false)

  if (isDirty.value) {
    addLog(`💾 正在自动保存配置...\n`, false)
    const saveSuccess = await saveConfig()
    if (!saveSuccess) {
      addLog(`❌ 配置保存失败，运行已中止。\n`, false)
      return
    }
    addLog(`✅ 配置已保存。\n`, false)
  }

  try {
    isRunning.value = true
    addLog(`🚀 正在运行脚本: ${selectedScript.value.name}...\n`, false)

    const cleanParams = {
      scriptName: String(selectedScript.value.name),
      configValues: JSON.parse(JSON.stringify(config.value.values || {}))
    }

    const result = await window.api.runJsScript(cleanParams)

    addLog(`\n=== 执行结果 ===\n`, false)
    if (result.stdout && result.stdout.trim()) {
      addLog(`📄 标准输出:\n${result.stdout}\n`, false)
    }
    if (result.stderr && result.stderr.trim()) {
      addLog(`⚠️ 错误输出:\n${result.stderr}\n`, false)
    }
    if (!result.stdout?.trim() && !result.stderr?.trim()) {
      addLog(`✅ 脚本执行完成，无输出内容。\n`, false)
    }
  } catch (error) {
    console.error('运行JS脚本时出错:', error)
    addLog(`\n❌ 脚本执行失败\n`, false)
    if (error.name) addLog(`🏷️ 错误类型: ${error.name}\n`, false)
    if (error.message) addLog(`💬 错误信息: ${error.message}\n`, false)
    if (error.exitCode !== undefined) addLog(`🔢 退出码: ${error.exitCode}\n`, false)
    if (error.debug) {
      addLog(`\n🔍 调试信息:\n${JSON.stringify(error.debug, null, 2)}\n`, false)
    }
  } finally {
    isRunning.value = false
  }
}

function clearLog() {
  setLog('💡 日志已清空。\n')
}

async function testNodeEnvironment() {
  try {
    isRunning.value = true
    setLog('🔍 正在测试Node.js环境...\n')
    const result = await window.api.testNodeEnvironment()
    if (result.success) {
      addLog(`✅ Node.js环境正常\n📦 版本: ${result.version}\n`, false)
    } else {
      addLog(`❌ Node.js环境异常\n⚠️ 错误: ${result.error}\n`, false)
    }
  } catch (error) {
    console.error('测试Node.js环境时出错:', error)
    addLog(`❌ 测试失败\n💬 错误: ${error.message}\n`, false)
  } finally {
    isRunning.value = false
  }
}

async function openAddConfigModal() {
  if (!selectedScript.value) {
    alert('请先选择一个脚本')
    return
  }
  configModalKey.value++
  newConfigField.value = {
    name: '',
    label: '',
    type: 'text',
    default: '',
    optionsText: ''
  }
  showAddConfigModal.value = true
  await nextTick()
  newConfigNameInput.value?.focus()
}

function closeAddConfigModal() {
  showAddConfigModal.value = false
  newConfigField.value = { name: '', label: '', type: 'text', default: '', optionsText: '' }
}

async function addConfigField() {
  const field = newConfigField.value
  if (!field.name.trim() || !field.label.trim()) {
    return alert('请填写配置名称和显示标签')
  }
  if (config.value.schema.some(f => f.name === field.name.trim())) {
    return alert('配置名称已存在，请使用其他名称')
  }
  const newField = {
    name: field.name.trim(),
    label: field.label.trim(),
    type: field.type
  }
  if (field.type === 'select') {
    const options = field.optionsText.split('\n').map(o => o.trim()).filter(o => o.length > 0)
    if (options.length === 0) {
      return alert('下拉选择类型需要至少一个选项')
    }
    newField.options = options
    newField.default = field.default || options[0]
  } else if (field.type === 'number') {
    newField.default = Number(field.default) || 0
  } else if (field.type === 'checkbox') {
    newField.default = Boolean(field.default)
  } else {
    newField.default = field.default || ''
  }
  config.value.schema.push(newField)
  config.value.values[newField.name] = newField.default
  isDirty.value = true
  closeAddConfigModal()
}

function promptDeleteConfigField(index) {
  const field = config.value.schema[index]
  if (!field) return
  configItemToDelete.value = { index, field }
  showDeleteConfirmModal.value = true
}

function cancelDeleteConfigField() {
  showDeleteConfirmModal.value = false
  configItemToDelete.value = null
}

async function confirmDeleteConfigField() {
  if (!configItemToDelete.value) return
  const { index, field } = configItemToDelete.value
  config.value.schema.splice(index, 1)
  delete config.value.values[field.name]
  isDirty.value = true
  showDeleteConfirmModal.value = false
  configItemToDelete.value = null
  await nextTick()
  addConfigBtn.value?.focus()
}
</script>

<style scoped>
.js-scripts-container {
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: #1a1a1a;
  color: #fff;
  overflow: hidden;
}
.script-list-panel {
  width: 250px;
  border-right: 1px solid #333;
  display: flex;
  flex-direction: column;
}
.panel-header {
  padding: 1rem;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.panel-header h3 {
  margin: 0;
}
.add-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}
.script-list-panel ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex-grow: 1;
}
.script-list-panel li {
  padding: 0.8rem 1rem;
  margin-bottom: 0.3rem;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease, color 0.2s ease;
}
.script-list-panel li:hover {
  background-color: #2c2c2c;
}
.script-list-panel li.active {
  background-color: #0056b3;
  color: white;
  font-weight: 500;
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
}
.delete-btn {
  background: none;
  border: none;
  color: #ff5555;
  cursor: pointer;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s;
}
.script-list-panel li:hover .delete-btn,
.script-list-panel li.active .delete-btn {
  visibility: visible;
  opacity: 1;
}
.right-panel {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.script-config-panel {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}
.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}
.title-input {
  font-size: 1.5rem;
  background: transparent;
  border: none;
  color: white;
  font-weight: bold;
}
.button-group button {
  margin-left: 10px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}
.run-btn {
  background-color: #007bff;
  color: white;
}
.run-btn:disabled {
  background-color: #555;
  cursor: not-allowed;
}
.save-btn {
  background-color: #555;
  color: #ccc;
}
.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.save-btn:not(:disabled) {
  background-color: #28a745;
  color: white;
}
.test-btn {
  padding: 8px 16px;
  background-color: #805ad5;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}
.test-btn:hover:not(:disabled) {
  background-color: #6b46c1;
}
.test-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}
.config-form {
  overflow-y: auto;
}
.form-group {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
}
.form-group label {
  margin-bottom: 0.5rem;
  color: #ccc;
}
.form-group input,
.form-group select {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #2c2c2c;
  color: #fff;
}
.form-checkbox {
  width: 20px;
  height: 20px;
  align-self: flex-start;
}
.no-config {
  text-align: center;
  padding: 2rem;
  color: #888;
}
.log-output-panel {
  flex-grow: 1;
  background-color: #0d1117;
  margin: 0 1rem 1rem 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #30363d;
  display: flex;
  flex-direction: column;
  min-height: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
.log-output-panel h4::before {
  content: '📋';
  margin-right: 8px;
}
.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}
.clear-log-btn {
  padding: 4px 10px;
  background-color: #666;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}
.clear-log-btn:hover:not(:disabled) {
  background-color: #777;
}
.clear-log-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
  opacity: 0.7;
}
.log-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  flex-grow: 1;
  overflow-y: auto;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #e6edf3;
  background-color: #010409;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #21262d;
  margin: 0;
  min-height: 150px;
  max-height: 400px;
}
.log-content::-webkit-scrollbar {
  width: 8px;
}
.log-content::-webkit-scrollbar-track {
  background: #21262d;
  border-radius: 4px;
}
.log-content::-webkit-scrollbar-thumb {
  background: #484f58;
  border-radius: 4px;
}
.log-content::-webkit-scrollbar-thumb:hover {
  background: #6e7681;
  font-size: 0.9rem;
  color: #ddd;
}
.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #888;
}
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-content {
  background-color: #2c2c2c;
  padding: 2rem;
  border-radius: 8px;
  width: 400px;
}
.modal-content h3 {
  margin-top: 0;
}
.modal-content input {
  width: 100%;
  padding: 0.5rem;
  margin-top: 1rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #1a1a1a;
  color: #fff;
}
.modal-actions {
  margin-top: 1.5rem;
  text-align: right;
}
.modal-actions button {
  margin-left: 10px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}
.modal-actions button:first-child {
  background-color: #666;
  color: white;
}
.modal-actions button:last-child {
  background-color: #1890ff;
  color: white;
}
.modal-actions .danger-btn {
  background-color: #ff4d4f;
  color: white;
}
.modal-actions .danger-btn:hover {
  background-color: #d9363e;
}
.config-modal {
  width: 500px;
  max-width: 90vw;
}
.config-form-modal .form-group {
  margin-bottom: 1rem;
}
.config-form-modal label {
  display: block;
  margin-bottom: 0.5rem;
  color: #fff;
  font-weight: 500;
}
.config-form-modal input,
.config-form-modal select,
.config-form-modal textarea {
  width: 100%;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #1a1a1a;
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
}
.config-form-modal input:focus,
.config-form-modal select:focus,
.config-form-modal textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.config-form-modal textarea {
  resize: vertical;
  min-height: 80px;
}
.config-section {
  margin-bottom: 1rem;
}
.config-header h4 {
  margin: 0;
  color: #fff;
  font-size: 16px;
}
.config-actions {
  display: flex;
  gap: 8px;
}
.add-config-btn {
  padding: 6px 12px;
  background-color: #52c41a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}
.add-config-btn:hover:not(:disabled) {
  background-color: #389e0d;
}
.add-config-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}
.form-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}
.remove-field-btn {
  padding: 4px 8px;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}
.remove-field-btn:hover:not(:disabled) {
  background-color: #d9363e;
}
.remove-field-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}
</style>