<template>
  <div class="scheduled-tasks-container">
    <div class="header">
      <h2>定时任务管理</h2>
      <p>创建和管理定时执行的脚本任务</p>
    </div>

    <div class="toolbar">
      <div class="search-section">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="搜索任务名称、脚本类型..."
          class="search-input"
        />
        <button @click="clearSearch" class="clear-search-btn" v-if="searchQuery">✕</button>
      </div>

      <div class="action-section">
        <div class="batch-actions" v-if="selectedTasks.length > 0">
          <span class="selected-count">已选择 {{ selectedTasks.length }} 个任务</span>
          <button @click="batchEnable" class="batch-btn enable-btn">批量启用</button>
          <button @click="batchDisable" class="batch-btn disable-btn">批量禁用</button>
          <button @click="batchDelete" class="batch-btn delete-btn">批量删除</button>
        </div>
        <button @click="openAddModal" class="add-btn">新增任务</button>
      </div>
    </div>

    <div class="tasks-content">
      <div class="tasks-list-panel">
        <div class="panel-header">
          <div class="list-controls">
            <label class="select-all-label">
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="toggleSelectAll"
                class="select-all-checkbox"
              />
              <span>全选</span>
            </label>
            <div class="task-count">共 {{ filteredTasks.length }} 个任务</div>
          </div>
        </div>

        <div class="tasks-list">
          <div v-if="filteredTasks.length === 0" class="empty-state">
            <div class="empty-icon">⏰</div>
            <p v-if="tasks.length === 0">暂无定时任务</p>
            <p v-else>没有找到匹配的任务</p>
            <span v-if="tasks.length === 0">点击"新增任务"创建您的第一个定时任务</span>
            <span v-else>尝试调整搜索条件</span>
          </div>

          <div v-else class="task-list-full">
            <div
              v-for="task in filteredTasks"
              :key="task.id"
              class="task-row-full"
              :class="{
                selected: selectedTasks.includes(task.id),
                running: task.status === 'running'
              }"
            >
              <div class="task-row-content-full">
                <!-- 选择框 -->
                <label class="task-checkbox" @click.stop>
                  <input
                    type="checkbox"
                    :checked="selectedTasks.includes(task.id)"
                    @change="toggleTaskSelection(task.id)"
                  />
                </label>

                <!-- 任务基本信息 -->
                <div class="task-basic-info">
                  <div class="task-name-with-badge">
                    <span class="task-name">{{ task.name }}</span>
                    <div class="task-type-badge" :class="task.taskType">
                      {{ getTaskTypeLabel(task.taskType) }}
                    </div>
                  </div>
                  <div class="task-script-info">
                    <span v-if="task.taskType !== 'http'" class="script-name">{{ task.scriptName }}</span>
                    <span v-else class="http-info">{{ task.httpConfig?.method }} {{ task.httpConfig?.url }}</span>
                  </div>
                </div>

                <!-- 调度信息 -->
                <div class="task-schedule-info">
                  <div class="schedule-text">{{ task.schedule }}</div>
                  <div class="next-run-text" v-if="task.enabled">
                    下次: {{ getNextRunTime(task) }}
                  </div>
                </div>

                <!-- 状态信息 -->
                <div class="task-status-info">
                  <div class="status-toggle-section">
                    <label class="toggle-switch" @click.stop>
                      <input
                        type="checkbox"
                        :checked="task.enabled"
                        @change="toggleTaskEnabled(task)"
                      />
                      <span class="toggle-slider"></span>
                    </label>
                    <span class="status-text" :class="{ enabled: task.enabled, disabled: !task.enabled }">
                      {{ task.enabled ? '已启用' : '已禁用' }}
                    </span>
                  </div>
                  <div class="last-run-info" v-if="task.last_run">
                    <span class="last-run-text">上次: {{ formatTime(task.last_run) }}</span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="task-actions-full">
                  <button @click.stop="runTaskNow(task)" class="action-btn run-btn" title="立即执行">▶️</button>
                  <button @click.stop="viewTaskLogs(task)" class="action-btn logs-btn" title="查看日志">📋</button>
                  <button @click.stop="editTask(task)" class="action-btn edit-btn" title="编辑">✏️</button>
                  <button @click.stop="deleteTask(task)" class="action-btn delete-btn" title="删除">🗑️</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 新增/编辑任务模态框 -->
    <div v-if="showAddModal || showEditModal" class="modal-overlay" @click.self="closeModal">
      <div class="modal-content large-modal">
        <div class="modal-header">
          <h3>{{ isEditMode ? '编辑定时任务' : '新增定时任务' }}</h3>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>

        <div class="modal-body">
          <div class="form-section">
            <h4>基本信息</h4>

            <div class="form-group">
              <label>任务名称 <span class="required">*</span></label>
              <input
                ref="taskNameInput"
                type="text"
                v-model="currentTask.name"
                placeholder="请输入任务名称"
                :class="{ error: errors.name }"
              />
              <span v-if="errors.name" class="error-text">{{ errors.name }}</span>
            </div>

            <div class="form-group">
              <label>任务类型 <span class="required">*</span></label>
              <div class="task-type-tabs">
                <button
                  v-for="type in taskTypes"
                  :key="type.value"
                  @click="currentTask.taskType = type.value"
                  class="task-type-tab"
                  :class="{ active: currentTask.taskType === type.value }"
                >
                  <span class="tab-icon">{{ type.icon }}</span>
                  <span class="tab-label">{{ type.label }}</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 脚本任务配置 -->
          <div v-if="currentTask.taskType !== 'http'" class="form-section">
            <h4>脚本配置</h4>

            <div class="form-group">
              <label>选择脚本 <span class="required">*</span></label>
              <div class="script-selector" :class="{ error: errors.scriptName }">
                <div class="script-input-wrapper">
                  <input
                    type="text"
                    :value="currentTask.scriptName || ''"
                    :placeholder="getScriptPlaceholder(currentTask.taskType)"
                    class="script-search-input"
                    @click="openScriptDropdown"
                    @keydown="handleScriptKeydown"
                    readonly
                  />
                  <button
                    type="button"
                    class="script-dropdown-btn"
                    @click="toggleScriptDropdown"
                    :disabled="scriptsLoading[currentTask.taskType]"
                  >
                    <span v-if="scriptsLoading[currentTask.taskType]" class="loading-spinner">⟳</span>
                    <span v-else class="dropdown-arrow">{{ showScriptDropdown ? '▲' : '▼' }}</span>
                  </button>
                </div>

                <div v-if="showScriptDropdown" class="script-dropdown" @click.stop>
                  <div class="script-search-wrapper">
                    <input
                      type="text"
                      v-model="scriptSearchQuery"
                      placeholder="搜索脚本..."
                      class="script-search"
                      @keydown="handleScriptKeydown"
                      ref="scriptSearchInput"
                    />
                  </div>

                  <div class="script-list">
                    <div v-if="scriptsLoading[currentTask.taskType]" class="script-loading">
                      <span class="loading-spinner">⟳</span>
                      <span>正在加载脚本...</span>
                    </div>

                    <div v-else-if="filteredScripts.length === 0" class="script-empty">
                      <span v-if="availableScripts[currentTask.taskType].length === 0">
                        暂无可用的{{ getTaskTypeLabel(currentTask.taskType) }}
                      </span>
                      <span v-else>
                        没有找到匹配的脚本
                      </span>
                    </div>

                    <div
                      v-else
                      v-for="(script, index) in filteredScripts"
                      :key="script.id || script.name"
                      class="script-item"
                      :class="{
                        selected: currentTask.scriptName === script.name,
                        highlighted: highlightedIndex === index
                      }"
                      @click="selectScript(script)"
                      @mouseenter="highlightedIndex = index"
                    >
                      <div class="script-name">{{ script.name }}</div>
                      <div class="script-path" v-if="script.path">{{ script.path }}</div>
                      <div class="script-description" v-if="script.description">{{ script.description }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <span v-if="errors.scriptName" class="error-text">{{ errors.scriptName }}</span>
            </div>
          </div>

          <!-- HTTP任务配置 -->
          <div v-if="currentTask.taskType === 'http'" class="form-section">
            <h4>HTTP请求配置</h4>

            <div class="form-row">
              <div class="form-group">
                <label>请求方法 <span class="required">*</span></label>
                <select v-model="currentTask.httpConfig.method">
                  <option value="GET">GET</option>
                  <option value="POST">POST</option>
                  <option value="PUT">PUT</option>
                  <option value="DELETE">DELETE</option>
                  <option value="PATCH">PATCH</option>
                </select>
              </div>

              <div class="form-group flex-grow">
                <label>请求URL <span class="required">*</span></label>
                <input
                  type="url"
                  v-model="currentTask.httpConfig.url"
                  placeholder="https://api.example.com/endpoint"
                  :class="{ error: errors.url }"
                />
                <span v-if="errors.url" class="error-text">{{ errors.url }}</span>
              </div>
            </div>

            <div class="form-group">
              <label>请求头 (JSON格式)</label>
              <textarea
                v-model="currentTask.httpConfig.headers"
                placeholder='{"Content-Type": "application/json", "Authorization": "Bearer token"}'
                rows="3"
              ></textarea>
            </div>

            <div class="form-group" v-if="['POST', 'PUT', 'PATCH'].includes(currentTask.httpConfig.method)">
              <label>请求体 (JSON格式)</label>
              <textarea
                v-model="currentTask.httpConfig.body"
                placeholder='{"key": "value"}'
                rows="4"
              ></textarea>
            </div>
          </div>

          <!-- 调度配置 -->
          <div class="form-section">
            <h4>调度配置</h4>

            <div class="form-group">
              <label>执行模式 <span class="required">*</span></label>
              <div class="radio-group">
                <label class="radio-item">
                  <input type="radio" v-model="currentTask.scheduleMode" value="repeat" />
                  <span>定时重复执行</span>
                </label>
                <label class="radio-item">
                  <input type="radio" v-model="currentTask.scheduleMode" value="once" />
                  <span>执行一次</span>
                </label>
              </div>
            </div>

            <!-- 执行一次配置 -->
            <div v-if="currentTask.scheduleMode === 'once'" class="form-group">
              <label>执行时间 <span class="required">*</span></label>
              <input
                type="datetime-local"
                v-model="currentTask.executeTime"
                :min="getCurrentDateTime()"
                :class="{ error: errors.executeTime }"
              />
              <span v-if="errors.executeTime" class="error-text">{{ errors.executeTime }}</span>
            </div>

            <!-- 重复执行配置 -->
            <div v-if="currentTask.scheduleMode === 'repeat'">
              <div class="form-group">
                <label>快捷选项</label>
                <div class="cron-presets">
                  <button
                    v-for="preset in cronPresets"
                    :key="preset.value"
                    @click="selectCronPreset(preset)"
                    class="preset-btn"
                    :class="{ active: currentTask.cronExpression === preset.value }"
                  >
                    {{ preset.label }}
                  </button>
                </div>
              </div>

              <div class="form-group">
                <label>Cron表达式 <span class="required">*</span></label>
                <input
                  type="text"
                  v-model="currentTask.cronExpression"
                  placeholder="* * * * * *"
                  :class="{ error: errors.cronExpression }"
                />
                <small>格式: 秒 分 时 日 月 周</small>
                <span v-if="errors.cronExpression" class="error-text">{{ errors.cronExpression }}</span>
              </div>

              <div class="cron-preview" v-if="currentTask.cronExpression">
                <span class="preview-label">下次执行时间:</span>
                <span class="preview-time">{{ getNextExecutionTime(currentTask.cronExpression) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button @click="closeModal" class="cancel-btn">取消</button>
          <button @click="saveTask" :disabled="!isFormValid" class="save-btn">
            {{ isEditMode ? '保存修改' : '创建任务' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 执行日志模态框 -->
    <div v-if="showLogModal" class="modal-overlay" @click.self="closeLogModal">
      <div class="modal-content log-modal">
        <div class="modal-header">
          <h3>执行日志 - {{ selectedTask?.name }}</h3>
          <button @click="closeLogModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body log-modal-body">
          <ExecutionLog
            :task-id="selectedTask?.id"
            :visible="showLogModal"
            @close="closeLogModal"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import ExecutionLog from './ExecutionLog.vue'

// 响应式数据
const tasks = ref([])
const selectedTask = ref(null)
const selectedTasks = ref([])
const showAddModal = ref(false)
const showEditModal = ref(false)
const showLogModal = ref(false)
const isEditMode = ref(false)
const searchQuery = ref('')
const errors = ref({})

// 可用脚本列表
const availableScripts = ref({
  cmd: [],
  js: [],
  python: []
})

// 脚本加载状态
const scriptsLoading = ref({
  cmd: false,
  js: false,
  python: false
})

// 脚本搜索
const scriptSearchQuery = ref('')
const showScriptDropdown = ref(false)
const highlightedIndex = ref(-1)
const scriptSearchInput = ref(null)
const taskNameInput = ref(null)

// 当前任务数据
const currentTask = ref({
  name: '',
  taskType: 'cmd',
  scriptName: '',
  scheduleMode: 'repeat',
  cronExpression: '0 0 * * *',
  executeTime: '',
  httpConfig: {
    method: 'GET',
    url: '',
    headers: '{}',
    body: '{}'
  },
  enabled: true
})

// 任务类型定义
const taskTypes = [
  { value: 'cmd', label: 'CMD脚本', icon: '📜' },
  { value: 'js', label: 'JS脚本', icon: '📄' },
  { value: 'python', label: 'Python脚本', icon: '🐍' },
  { value: 'http', label: 'HTTP请求', icon: '🌐' }
]

// Cron预设选项
const cronPresets = [
  { label: '每秒', value: '* * * * * *' },
  { label: '每分钟', value: '0 * * * * *' },
  { label: '每小时', value: '0 0 * * * *' },
  { label: '每天', value: '0 0 0 * * *' },
  { label: '每周', value: '0 0 0 * * 0' },
  { label: '每月', value: '0 0 0 1 * *' },
  { label: '工作日', value: '0 0 9 * * 1-5' }
]

// 计算属性
const filteredTasks = computed(() => {
  if (!searchQuery.value) return tasks.value

  const query = searchQuery.value.toLowerCase()
  return tasks.value.filter(task =>
    task.name.toLowerCase().includes(query) ||
    task.taskType.toLowerCase().includes(query) ||
    (task.scriptName && task.scriptName.toLowerCase().includes(query))
  )
})

const isAllSelected = computed(() => {
  return filteredTasks.value.length > 0 &&
         filteredTasks.value.every(task => selectedTasks.value.includes(task.id))
})

const filteredScripts = computed(() => {
  const scripts = availableScripts.value[currentTask.value.taskType] || []
  if (!scriptSearchQuery.value) return scripts

  const query = scriptSearchQuery.value.toLowerCase()
  return scripts.filter(script =>
    script.name.toLowerCase().includes(query) ||
    (script.path && script.path.toLowerCase().includes(query)) ||
    (script.description && script.description.toLowerCase().includes(query))
  )
})

const isFormValid = computed(() => {
  const task = currentTask.value

  if (!task.name.trim()) return '请输入任务名称'

  if (task.taskType === 'http') {
    if (!task.httpConfig.url.trim()) return '请输入请求URL'
    try {
      new URL(task.httpConfig.url)
    } catch {
      return '请输入有效的URL'
    }
  } else {
    if (!task.scriptName) return '请选择脚本'
  }

  if (task.scheduleMode === 'once') {
    if (!task.executeTime) return '请选择执行时间'
    if (new Date(task.executeTime) <= new Date()) return '执行时间必须是未来时间'
  } else {
    if (!task.cronExpression.trim()) return '请输入Cron表达式'
  }

  return true
})

// 基础操作函数
function openAddModal() {
  isEditMode.value = false
  showAddModal.value = true
  resetCurrentTask()
  clearErrors()

  // 在下一个tick中聚焦到任务名称输入框
  nextTick(() => {
    if (taskNameInput.value) {
      taskNameInput.value.focus()
    }
  })
}

function openEditModal(task) {
  isEditMode.value = true
  showEditModal.value = true
  currentTask.value = { ...task }
  clearErrors()
}

function closeModal() {
  showAddModal.value = false
  showEditModal.value = false
  resetCurrentTask()
  clearErrors()
}

function resetCurrentTask() {
  currentTask.value = {
    name: '',
    taskType: 'cmd',
    scriptName: '',
    scheduleMode: 'repeat',
    cronExpression: '0 0 * * *',
    executeTime: '',
    httpConfig: {
      method: 'GET',
      url: '',
      headers: '{}',
      body: '{}'
    },
    enabled: true
  }

  // 重置脚本搜索状态
  scriptSearchQuery.value = ''
  closeScriptDropdown()
}

function clearErrors() {
  errors.value = {}
}

function clearSearch() {
  searchQuery.value = ''
}

// 任务操作函数
function editTask(task) {
  openEditModal(task)
}

function viewTaskLogs(task) {
  selectedTask.value = task
  showLogModal.value = true
}

async function toggleTaskEnabled(task) {
  try {
    const result = await window.api.toggleScheduledTaskEnabled(task.id, !task.enabled)
    if (result.success) {
      task.enabled = !task.enabled
      console.log(`任务 "${task.name}" ${task.enabled ? '已启用' : '已禁用'}`)
    } else {
      console.error('切换任务状态失败:', result.error)
      alert('切换任务状态失败: ' + result.error)
    }
  } catch (error) {
    console.error('切换任务状态异常:', error)
    alert('切换任务状态异常: ' + error.message)
  }
}

async function deleteTask(task) {
  if (confirm(`确定要删除任务"${task.name}"吗？此操作不可撤销！`)) {
    try {
      const result = await window.api.deleteScheduledTask(task.id)
      if (result.success) {
        console.log(`任务 "${task.name}" 已删除`)
        await loadTasks() // 重新加载任务列表

        // 清理选中状态
        if (selectedTask.value && selectedTask.value.id === task.id) {
          selectedTask.value = null
        }
        const selectedIndex = selectedTasks.value.indexOf(task.id)
        if (selectedIndex > -1) {
          selectedTasks.value.splice(selectedIndex, 1)
        }
      } else {
        console.error('删除任务失败:', result.error)
        alert('删除任务失败: ' + result.error)
      }
    } catch (error) {
      console.error('删除任务异常:', error)
      alert('删除任务异常: ' + error.message)
    }
  }
}

async function runTaskNow(task) {
  if (!task) {
    task = selectedTask.value
  }

  if (!task) {
    console.error('No task selected for execution')
    alert('请先选择一个任务')
    return
  }

  if (!task.id) {
    console.error('Task missing ID:', task)
    alert('任务数据异常：缺少ID')
    return
  }

  console.log('立即执行任务:', task.name)

  const startTime = new Date().toISOString()
  const logData = {
    taskId: task.id.toString(),
    taskName: task.name || '未命名任务',
    taskType: task.taskType || 'unknown',
    status: 'running',
    startTime: startTime,
    endTime: null,
    durationMs: null,
    output: null,
    errorMessage: null,
    exitCode: null
  }

  try {
    // 添加开始执行的日志
    const logResult = await window.api.addScheduledTaskLog(logData)
    const logId = logResult.success ? logResult.logId : null

    // 更新任务状态
    task.status = 'running'
    task.lastRun = startTime

    let executionResult
    const executionStartTime = Date.now()

    // 根据任务类型执行不同的逻辑
    if (task.taskType === 'cmd') {
      executionResult = await executeCommandTask(task)
    } else if (task.taskType === 'js') {
      executionResult = await executeJsTask(task)
    } else if (task.taskType === 'python') {
      executionResult = await executePythonTask(task)
    } else if (task.taskType === 'http') {
      executionResult = await executeHttpTask(task)
    } else {
      throw new Error(`Unsupported task type: ${task.taskType}`)
    }

    const executionEndTime = Date.now()
    const durationMs = executionEndTime - executionStartTime
    const endTime = new Date().toISOString()

    // 更新任务状态
    task.status = executionResult.success ? 'idle' : 'error'
    task.lastRun = endTime

    // 更新日志状态
    if (logId) {
      await window.api.updateScheduledTaskLogStatus(
        logId,
        executionResult.success ? 'success' : 'error',
        endTime,
        durationMs,
        executionResult.output || null,
        executionResult.error || null,
        executionResult.exitCode || null
      )
    }

    console.log('任务执行完成:', {
      taskName: task.name,
      success: executionResult.success,
      duration: `${(durationMs / 1000).toFixed(1)}秒`
    })

  } catch (error) {
    console.error('任务执行失败:', error)

    const endTime = new Date().toISOString()
    const durationMs = Date.now() - new Date(startTime).getTime()

    // 更新任务状态
    task.status = 'error'
    task.lastRun = endTime

    // 更新日志状态
    if (logResult.success && logResult.logId) {
      await window.api.updateScheduledTaskLogStatus(
        logResult.logId,
        'error',
        endTime,
        durationMs,
        null,
        error.message,
        null
      )
    }

    // 显示错误提示
    alert(`任务执行失败: ${error.message}`)
  }
}

function closeLogModal() {
  showLogModal.value = false
}

// 任务执行函数
async function executeCommandTask(task) {
  try {
    // 这里需要根据实际的CMD脚本执行API来实现
    // 暂时返回模拟结果
    return {
      success: true,
      output: `CMD脚本 "${task.scriptName}" 执行成功\n执行时间: ${new Date().toLocaleString()}`,
      error: null,
      exitCode: 0
    }
  } catch (error) {
    return {
      success: false,
      output: null,
      error: error.message,
      exitCode: 1
    }
  }
}

async function executeJsTask(task) {
  try {
    // 这里需要根据实际的JS脚本执行API来实现
    // 暂时返回模拟结果
    return {
      success: true,
      output: `JS脚本 "${task.scriptName}" 执行成功\n执行时间: ${new Date().toLocaleString()}`,
      error: null,
      exitCode: 0
    }
  } catch (error) {
    return {
      success: false,
      output: null,
      error: error.message,
      exitCode: 1
    }
  }
}

async function executePythonTask(task) {
  try {
    // 这里需要根据实际的Python脚本执行API来实现
    // 暂时返回模拟结果
    return {
      success: true,
      output: `Python脚本 "${task.scriptName}" 执行成功\n执行时间: ${new Date().toLocaleString()}`,
      error: null,
      exitCode: 0
    }
  } catch (error) {
    return {
      success: false,
      output: null,
      error: error.message,
      exitCode: 1
    }
  }
}

async function executeHttpTask(task) {
  try {
    const { method, url, headers, body } = task.httpConfig

    const requestOptions = {
      method: method,
      headers: headers ? JSON.parse(headers) : {}
    }

    if (['POST', 'PUT', 'PATCH'].includes(method) && body) {
      requestOptions.body = body
    }

    const response = await fetch(url, requestOptions)
    const responseText = await response.text()

    return {
      success: response.ok,
      output: `HTTP请求执行${response.ok ? '成功' : '失败'}\n状态码: ${response.status}\n响应: ${responseText}`,
      error: response.ok ? null : `HTTP ${response.status}: ${response.statusText}`,
      exitCode: response.ok ? 0 : response.status
    }
  } catch (error) {
    return {
      success: false,
      output: null,
      error: error.message,
      exitCode: 1
    }
  }
}

// 批量操作函数
function toggleTaskSelection(taskId) {
  const index = selectedTasks.value.indexOf(taskId)
  if (index > -1) {
    selectedTasks.value.splice(index, 1)
  } else {
    selectedTasks.value.push(taskId)
  }
}

function toggleSelectAll() {
  if (isAllSelected.value) {
    selectedTasks.value = []
  } else {
    selectedTasks.value = filteredTasks.value.map(task => task.id)
  }
}

async function batchEnable() {
  if (confirm(`确定要启用选中的 ${selectedTasks.value.length} 个任务吗？`)) {
    try {
      const promises = selectedTasks.value.map(async taskId => {
        const result = await window.api.toggleScheduledTaskEnabled(taskId, true)
        if (!result.success) {
          console.error(`启用任务 ${taskId} 失败:`, result.error)
          return false
        }
        return true
      })

      const results = await Promise.all(promises)
      const successCount = results.filter(r => r).length

      if (successCount > 0) {
        await loadTasks() // 重新加载任务列表
        console.log(`成功启用 ${successCount} 个任务`)
      }

      selectedTasks.value = []
    } catch (error) {
      console.error('批量启用任务异常:', error)
      alert('批量启用任务异常: ' + error.message)
    }
  }
}

async function batchDisable() {
  if (confirm(`确定要禁用选中的 ${selectedTasks.value.length} 个任务吗？`)) {
    try {
      const promises = selectedTasks.value.map(async taskId => {
        const result = await window.api.toggleScheduledTaskEnabled(taskId, false)
        if (!result.success) {
          console.error(`禁用任务 ${taskId} 失败:`, result.error)
          return false
        }
        return true
      })

      const results = await Promise.all(promises)
      const successCount = results.filter(r => r).length

      if (successCount > 0) {
        await loadTasks() // 重新加载任务列表
        console.log(`成功禁用 ${successCount} 个任务`)
      }

      selectedTasks.value = []
    } catch (error) {
      console.error('批量禁用任务异常:', error)
      alert('批量禁用任务异常: ' + error.message)
    }
  }
}

async function batchDelete() {
  if (confirm(`确定要删除选中的 ${selectedTasks.value.length} 个任务吗？此操作不可撤销！`)) {
    try {
      const promises = selectedTasks.value.map(async taskId => {
        const result = await window.api.deleteScheduledTask(taskId)
        if (!result.success) {
          console.error(`删除任务 ${taskId} 失败:`, result.error)
          return false
        }
        return true
      })

      const results = await Promise.all(promises)
      const successCount = results.filter(r => r).length

      if (successCount > 0) {
        await loadTasks() // 重新加载任务列表
        console.log(`成功删除 ${successCount} 个任务`)
      }

      selectedTasks.value = []
      selectedTask.value = null
    } catch (error) {
      console.error('批量删除任务异常:', error)
      alert('批量删除任务异常: ' + error.message)
    }
  }
}

// 表单操作函数
async function saveTask() {
  if (isFormValid.value !== true) return

  try {
    // 创建一个可序列化的任务数据对象
    const taskData = {
      name: currentTask.value.name,
      taskType: currentTask.value.taskType,
      scriptName: currentTask.value.scriptName || null,
      scheduleMode: currentTask.value.scheduleMode,
      cronExpression: currentTask.value.cronExpression || null,
      executeTime: currentTask.value.executeTime || null,
      httpConfig: currentTask.value.taskType === 'http' ? {
        method: currentTask.value.httpConfig.method,
        url: currentTask.value.httpConfig.url,
        headers: currentTask.value.httpConfig.headers,
        body: currentTask.value.httpConfig.body
      } : null,
      enabled: Boolean(currentTask.value.enabled)
    }

    let result
    if (isEditMode.value) {
      result = await window.api.updateScheduledTask(currentTask.value.id, taskData)
    } else {
      result = await window.api.createScheduledTask(taskData)
    }

    if (result.success) {
      console.log(isEditMode.value ? '任务更新成功' : '任务创建成功')
      await loadTasks() // 重新加载任务列表
      closeModal()
    } else {
      console.error('保存任务失败:', result.error)
      alert('保存任务失败: ' + result.error)
    }
  } catch (error) {
    console.error('保存任务异常:', error)
    alert('保存任务异常: ' + error.message)
  }
}

function selectCronPreset(preset) {
  currentTask.value.cronExpression = preset.value
}

// 工具函数
function getTaskTypeLabel(taskType) {
  const type = taskTypes.find(t => t.value === taskType)
  return type ? type.label : taskType
}

// API加载函数
async function loadScripts(taskType) {
  if (scriptsLoading.value[taskType]) return

  scriptsLoading.value[taskType] = true

  try {
    let scripts = []

    switch (taskType) {
      case 'cmd':
        scripts = await window.api.getCmdScripts()
        break
      case 'js':
        scripts = await window.api.getJsScripts()
        break
      case 'python':
        scripts = await window.api.getPythonScripts()
        break
    }

    // 转换脚本数据格式，确保包含必要的字段
    availableScripts.value[taskType] = scripts.map(script => ({
      id: script.id,
      name: script.name,
      path: script.path || script.name,
      description: script.description || '',
      content: script.content || ''
    }))

  } catch (error) {
    console.error(`Failed to load ${taskType} scripts:`, error)
    // 显示错误提示
    availableScripts.value[taskType] = []
  } finally {
    scriptsLoading.value[taskType] = false
  }
}

// 脚本下拉框操作函数
function openScriptDropdown() {
  if (showScriptDropdown.value) return

  showScriptDropdown.value = true
  highlightedIndex.value = -1

  // 如果脚本列表为空，则加载脚本
  if (availableScripts.value[currentTask.value.taskType].length === 0) {
    loadScripts(currentTask.value.taskType)
  }

  // 聚焦到搜索框
  nextTick(() => {
    if (scriptSearchInput.value) {
      scriptSearchInput.value.focus()
    }
  })

  // 添加全局点击监听器
  document.addEventListener('click', handleClickOutside)
}

function closeScriptDropdown() {
  showScriptDropdown.value = false
  scriptSearchQuery.value = ''
  highlightedIndex.value = -1

  // 移除全局点击监听器
  document.removeEventListener('click', handleClickOutside)
}

function toggleScriptDropdown() {
  if (showScriptDropdown.value) {
    closeScriptDropdown()
  } else {
    openScriptDropdown()
  }
}

function handleClickOutside(event) {
  const scriptSelector = event.target.closest('.script-selector')
  if (!scriptSelector) {
    closeScriptDropdown()
  }
}

function selectScript(script) {
  currentTask.value.scriptName = script.name
  closeScriptDropdown()

  // 清除错误状态
  if (errors.value.scriptName) {
    delete errors.value.scriptName
  }
}

function handleScriptKeydown(event) {
  const scripts = filteredScripts.value

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, scripts.length - 1)
      break

    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
      break

    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0 && scripts[highlightedIndex.value]) {
        selectScript(scripts[highlightedIndex.value])
      }
      break

    case 'Escape':
      event.preventDefault()
      closeScriptDropdown()
      break
  }
}

function getScriptPlaceholder(taskType) {
  const typeLabels = {
    cmd: 'CMD脚本',
    js: 'JS脚本',
    python: 'Python脚本'
  }
  return `请选择${typeLabels[taskType] || '脚本'}`
}



function getNextRunTime(task) {
  // 这里应该根据任务的调度计划计算下次执行时间
  // 简化实现，返回一个示例时间
  if (!task.enabled) return '已禁用'
  if (task.scheduleMode === 'once') {
    return formatTime(task.executeTime)
  }
  return '2024-07-11 18:00:00'
}

function getNextExecutionTime(cronExpression) {
  // 这里应该解析Cron表达式并计算下次执行时间
  // 简化实现，返回一个示例时间
  if (!cronExpression) return '无效表达式'
  return '2024-07-11 18:00:00'
}

function getCurrentDateTime() {
  const now = new Date()
  now.setMinutes(now.getMinutes() - now.getTimezoneOffset())
  return now.toISOString().slice(0, 16)
}

function formatTime(timeString) {
  if (!timeString) return '未设置'
  return new Date(timeString).toLocaleString('zh-CN')
}

// 加载任务列表
async function loadTasks() {
  try {
    const taskList = await window.api.getScheduledTasks()
    tasks.value = taskList.map(task => ({
      ...task,
      taskType: task.task_type,
      scriptName: task.script_name,
      scheduleMode: task.schedule_mode,
      cronExpression: task.cron_expression,
      executeTime: task.execute_time,
      lastRun: task.last_run,
      createdAt: task.created_at,
      updatedAt: task.updated_at
    }))
    console.log('已加载任务列表:', tasks.value.length, '个任务')
  } catch (error) {
    console.error('加载任务列表失败:', error)
    tasks.value = []
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 预加载CMD脚本（默认任务类型）
  loadScripts('cmd')

  // 加载任务列表
  loadTasks()
})

// 组件卸载时清理
onUnmounted(() => {
  // 移除全局点击监听器
  document.removeEventListener('click', handleClickOutside)
})

// 监听任务类型变化，自动加载对应脚本
watch(() => currentTask.value.taskType, (newType, oldType) => {
  if (newType !== oldType && newType !== 'http') {
    // 重置脚本选择
    currentTask.value.scriptName = ''
    scriptSearchQuery.value = ''

    // 加载新类型的脚本
    if (availableScripts.value[newType].length === 0) {
      loadScripts(newType)
    }
  }
})

// 监听脚本搜索查询变化，重置高亮索引
watch(scriptSearchQuery, () => {
  highlightedIndex.value = -1
})
</script>

<style scoped>
.scheduled-tasks-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #1a1a1a;
  color: #e0e0e0;
  overflow: hidden;
}

.header {
  padding: 24px;
  border-bottom: 1px solid #333;
}

.header h2 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #ffffff;
}

.header p {
  font-size: 14px;
  color: #b0b0b0;
  margin: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #333;
  background-color: #1f1f1f;
}

.search-section {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 10px 16px;
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  color: #e0e0e0;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #4a90e2;
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.clear-search-btn:hover {
  color: #e0e0e0;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #444;
}

.selected-count {
  font-size: 13px;
  color: #4a90e2;
  font-weight: 500;
}

.batch-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.batch-btn.enable-btn {
  background-color: #4caf50;
  color: white;
}

.batch-btn.enable-btn:hover {
  background-color: #45a049;
}

.batch-btn.disable-btn {
  background-color: #ff9800;
  color: white;
}

.batch-btn.disable-btn:hover {
  background-color: #f57c00;
}

.batch-btn.delete-btn {
  background-color: #f44336;
  color: white;
}

.batch-btn.delete-btn:hover {
  background-color: #d32f2f;
}

.add-btn {
  background-color: #4a90e2;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.add-btn:hover {
  background-color: #357abd;
}

.tasks-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.tasks-list-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #333;
}

.list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.select-all-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #e0e0e0;
  cursor: pointer;
}

.select-all-checkbox {
  width: 16px;
  height: 16px;
}

.task-count {
  font-size: 13px;
  color: #888;
}

.add-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.add-btn:hover {
  background-color: #45a049;
}

.tasks-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: #888;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 8px;
  color: #b0b0b0;
}

.empty-state span {
  font-size: 14px;
  color: #888;
}

.task-list-full {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-row-full {
  background-color: #2a2a2a;
  border: 1px solid #3a3a3a;
  border-radius: 8px;
  transition: all 0.2s ease;
  overflow: hidden;
}

.task-row-full:hover {
  border-color: #4a4a4a;
  background-color: #2f2f2f;
}

.task-row-full.selected {
  border-color: #4a90e2;
  background-color: #1e3a5f;
}

.task-row-full.running {
  border-color: #4caf50;
  background-color: #1e3f1e;
}

.task-row-content-full {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  gap: 20px;
}

.task-checkbox {
  flex-shrink: 0;
  cursor: pointer;
}

.task-checkbox input {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* 任务基本信息区域 */
.task-basic-info {
  flex: 2;
  min-width: 0;
}

.task-name-with-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.task-name {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task-script-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.script-name, .http-info {
  font-size: 13px;
  color: #b0b0b0;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 调度信息区域 */
.task-schedule-info {
  flex: 1.5;
  min-width: 0;
  text-align: center;
}

.schedule-text {
  font-size: 14px;
  color: #4a90e2;
  font-weight: 500;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  margin-bottom: 4px;
}

.next-run-text {
  font-size: 12px;
  color: #888;
}

/* 状态信息区域 */
.task-status-info {
  flex: 1;
  text-align: center;
}

.status-toggle-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 4px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #666;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.toggle-switch input:checked + .toggle-slider {
  background-color: #4caf50;
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}

.status-text.enabled {
  color: #4caf50;
}

.status-text.disabled {
  color: #888;
}

.last-run-info {
  margin-top: 4px;
}

.last-run-text {
  font-size: 11px;
  color: #888;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* 操作按钮区域 */
.task-actions-full {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.task-type-badge {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.task-type-badge.cmd {
  background-color: #ff9800;
  color: #fff;
}

.task-type-badge.js {
  background-color: #f7df1e;
  color: #000;
}

.task-type-badge.python {
  background-color: #3776ab;
  color: #fff;
}

.task-type-badge.http {
  background-color: #4caf50;
  color: #fff;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background-color: #3a3a3a;
  color: #e0e0e0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: #4a4a4a;
  transform: scale(1.05);
}

.action-btn.run-btn:hover {
  background-color: #ff9800;
}

.action-btn.logs-btn:hover {
  background-color: #4a90e2;
}

.action-btn.edit-btn:hover {
  background-color: #2196f3;
}

.action-btn.toggle-btn:hover {
  background-color: #4caf50;
}

.action-btn.delete-btn:hover {
  background-color: #f44336;
}



.status-indicator {
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 16px;
  font-weight: 500;
  display: inline-block;
}

.status-indicator.enabled {
  background-color: #4caf50;
  color: white;
}

.status-indicator.disabled {
  background-color: #666;
  color: #ccc;
}

.status-indicator.running {
  background-color: #ff9800;
  color: white;
}



.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #1a1a1a;
  border-radius: 12px;
  width: 600px;
  max-width: 90vw;
  max-height: 90vh;
  border: 1px solid #333;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
}

.modal-content.large-modal {
  width: 800px;
}

.modal-content.log-modal {
  width: 1000px;
  height: 80vh;
}

.log-modal-body {
  padding: 0;
  height: 100%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  margin-bottom: 20px;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #ffffff;
  font-weight: 600;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background-color: #3a3a3a;
  color: #e0e0e0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background-color: #f44336;
}

.modal-body {
  flex: 1;
  padding: 0 24px;
  overflow-y: auto;
}

.modal-footer {
  padding: 20px 24px 24px 24px;
  border-top: 1px solid #333;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #ffffff;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 1px solid #333;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  color: #e0e0e0;
  margin-bottom: 8px;
  font-weight: 500;
}

.required {
  color: #f44336;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #444;
  background-color: #2a2a2a;
  color: #e0e0e0;
  font-size: 14px;
  box-sizing: border-box;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #f44336;
}

.error-text {
  display: block;
  font-size: 12px;
  color: #f44336;
  margin-top: 4px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-row .form-group {
  flex: 1;
}

.form-row .form-group.flex-grow {
  flex: 2;
}

.task-type-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.task-type-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 1px solid #444;
  border-radius: 8px;
  background-color: #2a2a2a;
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.task-type-tab:hover {
  border-color: #4a90e2;
  background-color: #333;
}

.task-type-tab.active {
  border-color: #4a90e2;
  background-color: #1e3a5f;
  color: #ffffff;
}

.tab-icon {
  font-size: 16px;
}

.tab-label {
  font-size: 14px;
  font-weight: 500;
}

.radio-group {
  display: flex;
  gap: 20px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #e0e0e0;
}

.radio-item input[type="radio"] {
  width: 16px;
  height: 16px;
  margin: 0;
}

.cron-presets {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.preset-btn {
  padding: 8px 12px;
  border: 1px solid #444;
  border-radius: 6px;
  background-color: #2a2a2a;
  color: #e0e0e0;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.preset-btn:hover {
  border-color: #4a90e2;
  background-color: #333;
}

.preset-btn.active {
  border-color: #4a90e2;
  background-color: #1e3a5f;
  color: #ffffff;
}

.cron-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #1f1f1f;
  border-radius: 8px;
  border: 1px solid #333;
  margin-top: 12px;
}

.preview-label {
  font-size: 13px;
  color: #888;
}

.preview-time {
  font-size: 13px;
  color: #4a90e2;
  font-weight: 500;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.interval-inputs {
  display: flex;
  gap: 8px;
}

.interval-inputs input {
  flex: 1;
}

.interval-inputs select {
  flex: 1;
}

.form-group small {
  display: block;
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #333;
}

.modal-actions button {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.modal-actions button:first-child {
  background-color: #666;
  color: white;
}

.modal-actions button:first-child:hover {
  background-color: #777;
}

.modal-actions button:last-child {
  background-color: #4caf50;
  color: white;
}

.modal-actions button:last-child:hover:not(:disabled) {
  background-color: #45a049;
}

.cancel-btn {
  padding: 12px 24px;
  border: 1px solid #666;
  border-radius: 8px;
  background-color: transparent;
  color: #e0e0e0;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  border-color: #888;
  background-color: #333;
}

.save-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  background-color: #4a90e2;
  color: #ffffff;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.save-btn:hover:not(:disabled) {
  background-color: #357abd;
}

.save-btn:disabled {
  background-color: #444;
  color: #888;
  cursor: not-allowed;
}

.form-group small {
  display: block;
  font-size: 12px;
  color: #888;
  margin-top: 4px;
  font-style: italic;
}

/* 脚本选择器样式 */
.script-selector {
  position: relative;
}

.script-selector.error .script-search-input {
  border-color: #f44336;
}

.script-input-wrapper {
  position: relative;
  display: flex;
}

.script-search-input {
  flex: 1;
  padding-right: 40px;
  cursor: pointer;
}

.script-dropdown-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: #888;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.script-dropdown-btn:hover {
  background-color: #3a3a3a;
  color: #e0e0e0;
}

.script-dropdown-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.script-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  max-height: 300px;
  overflow: hidden;
  margin-top: 4px;
}

.script-search-wrapper {
  padding: 12px;
  border-bottom: 1px solid #333;
}

.script-search {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #444;
  border-radius: 6px;
  background-color: #1a1a1a;
  color: #e0e0e0;
  font-size: 14px;
  box-sizing: border-box;
}

.script-search:focus {
  outline: none;
  border-color: #4a90e2;
}

.script-list {
  max-height: 200px;
  overflow-y: auto;
}

.script-loading,
.script-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #888;
  font-size: 14px;
}

.script-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #333;
  transition: background-color 0.2s ease;
}

.script-item:last-child {
  border-bottom: none;
}

.script-item:hover,
.script-item.highlighted {
  background-color: #3a3a3a;
}

.script-item.selected {
  background-color: #1e3a5f;
  color: #ffffff;
}

.script-name {
  font-size: 14px;
  font-weight: 500;
  color: #e0e0e0;
  margin-bottom: 4px;
}

.script-item.selected .script-name {
  color: #ffffff;
}

.script-path {
  font-size: 12px;
  color: #888;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  margin-bottom: 2px;
}

.script-item.selected .script-path {
  color: #b0b0b0;
}

.script-description {
  font-size: 11px;
  color: #666;
  font-style: italic;
}

.script-item.selected .script-description {
  color: #999;
}

/* 滚动条样式 */
.script-list::-webkit-scrollbar {
  width: 6px;
}

.script-list::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.script-list::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 3px;
}

.script-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
