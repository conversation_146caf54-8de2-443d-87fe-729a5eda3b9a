<template>
  <div class="settings">
    <div class="header">
      <h2>系统设置</h2>
      <p>配置应用程序的基本设置</p>
    </div>
    
    <div class="settings-container">
      <div class="settings-section">
        <h3 class="section-title">基本设置</h3>
        
        <div class="setting-card">
          <div class="setting-header">
            <h4>应用主题</h4>
            <div class="toggle-switch-placeholder active">
              <div class="switch"></div>
            </div>
          </div>
          <p class="setting-desc">使用深色主题</p>
        </div>
        
        <div class="setting-card">
          <div class="setting-header">
            <h4>自动更新</h4>
            <div class="toggle-switch-placeholder active">
              <div class="switch"></div>
            </div>
          </div>
          <p class="setting-desc">自动检查并安装应用更新</p>
        </div>
        
        <div class="setting-card">
          <div class="setting-header">
            <h4>开机自启</h4>
            <div class="toggle-switch-placeholder">
              <div class="switch"></div>
            </div>
          </div>
          <p class="setting-desc">系统启动时自动运行应用</p>
        </div>
      </div>
      
      <div class="settings-section">
        <h3 class="section-title">数据设置</h3>
        
        <div class="setting-card">
          <div class="setting-header">
            <h4>数据存储位置</h4>
          </div>
          <p class="setting-desc">选择应用数据的存储位置</p>
          <div class="setting-control">
            <input type="text" value="C:\Users\<USER>\AppData\Roaming\electron-qingfeng" readonly>
            <button class="browse-btn">浏览...</button>
          </div>
        </div>
        
        <div class="setting-card">
          <div class="setting-header">
            <h4>自动备份</h4>
            <div class="toggle-switch-placeholder active">
              <div class="switch"></div>
            </div>
          </div>
          <p class="setting-desc">定期备份应用数据</p>
          <div class="setting-control backup-interval">
            <label>备份频率:</label>
            <select>
              <option>每天</option>
              <option selected>每周</option>
              <option>每月</option>
            </select>
          </div>
        </div>
      </div>
      
      <div class="settings-section">
        <h3 class="section-title">关于</h3>
        
        <div class="setting-card about-card">
          <div class="about-info">
            <p><strong>清风智检</strong> v1.0.0</p>
            <p>© 2023 清风智检团队</p>
          </div>
          <div class="about-actions">
            <button class="check-update-btn">检查更新</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Settings component
</script>

<style scoped>
.settings {
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.9);
  padding: 24px;
  box-sizing: border-box;
  overflow-y: auto;
  letter-spacing: 0.2px;
}

.header h2 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #ffffff;
  letter-spacing: 0.3px;
}

.header p {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 24px;
}

.settings-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  padding: 16px;
  margin: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.9);
}

.setting-card {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.setting-card:last-child {
  border-bottom: none;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.setting-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.setting-desc {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 12px 0;
}

.setting-control {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.setting-control input {
  flex: 1;
  padding: 8px 12px;
  background-color: #2c2c2c;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
}

.browse-btn {
  background-color: #2c2c2c;
  border: 1px solid #3a3a3a;
  color: rgba(255, 255, 255, 0.9);
  padding: 0 12px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.browse-btn:hover {
  background-color: #3a3a3a;
}

.backup-interval {
  display: flex;
  align-items: center;
  gap: 12px;
}

.backup-interval label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
}

.backup-interval select {
  padding: 6px 10px;
  background-color: #2c2c2c;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
}

.about-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.about-info p {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
}

.about-info p:last-child {
  margin-bottom: 0;
}

.check-update-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.check-update-btn:hover {
  background-color: #40a9ff;
}

/* 开关样式 */
.toggle-switch-placeholder {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.toggle-switch-placeholder .switch {
  width: 38px;
  height: 20px;
  background-color: #3a3a3a;
  border-radius: 10px;
  position: relative;
  transition: background-color 0.2s;
}

.toggle-switch-placeholder .switch::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #ffffff;
  top: 2px;
  left: 2px;
  transition: transform 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.toggle-switch-placeholder.active .switch {
  background-color: #1890ff;
}

.toggle-switch-placeholder.active .switch::before {
  transform: translateX(18px);
}
</style> 