<template>
  <div class="sidebar-menu">
    <div class="menu-header">
      <div class="logo-section">
        <img src="../assets/logo.ico" alt="Logo" class="logo" />
        <span class="app-title">清风系统</span>
      </div>
    </div>

    <div class="menu-content">
      <ul>
        <li
          v-for="item in menuItems"
          :key="item.name"
          :class="{
            active: props.activeMenu === item.name || (item.children && item.children.some(child => child.name === props.activeMenu)),
            'has-children': item.children,
            expanded: item.children && expandedMenus.includes(item.name)
          }"
          @click="handleMenuClick(item)"
        >
          <div class="menu-item-content">
            <span class="icon">{{ item.icon }}</span>
            <span class="text">{{ item.label }}</span>
            <span v-if="item.children" class="expand-icon">
              {{ expandedMenus.includes(item.name) ? '▼' : '▶' }}
            </span>
          </div>

          <!-- 二级菜单 -->
          <ul v-if="item.children && expandedMenus.includes(item.name)" class="submenu">
            <li
              v-for="child in item.children"
              :key="child.name"
              :class="{ active: props.activeMenu === child.name }"
              @click.stop="activateMenu(child.name)"
            >
              <span class="icon">{{ child.icon }}</span>
              <span class="text">{{ child.label }}</span>
            </li>
          </ul>
        </li>
      </ul>
    </div>

    <div class="menu-footer">
      <div class="reload-section">
        <button @click="reloadApp" class="reload-btn" title="热重载Vue项目" :disabled="isReloading">
          <span class="reload-icon" :class="{ 'spinning': isReloading }">🔄</span>
          <span class="reload-text">{{ isReloading ? '重载中...' : '热重载' }}</span>
        </button>
      </div>

      <div class="footer-info-line">
        <span>v1.0.0</span>
        <span>{{ currentTime }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  activeMenu: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['menu-click'])

const currentTime = ref('')
const currentDate = ref('')
const expandedMenus = ref(['ScriptManagement']) // 默认展开脚本管理
const isReloading = ref(false)

const menuItems = computed(() => [
  { name: 'Workbench', label: '工作台', icon: '💼' },
  {
    name: 'ScriptManagement',
    label: '脚本管理',
    icon: '📁',
    children: [
      { name: 'CmdScripts', label: 'CMD脚本', icon: '📜' },
      { name: 'JsScripts', label: 'JS脚本', icon: '📄' },
      { name: 'PythonScripts', label: 'Python脚本', icon: '🐍' }
    ]
  },
  { name: 'ScheduledTasks', label: '定时任务', icon: '⏰' },
  { name: 'Hotkeys', label: '快捷键', icon: '⌨️' },
  { name: 'Settings', label: '设置', icon: '⚙️' }
])

function handleMenuClick(item) {
  if (item.children) {
    // 切换展开/收起状态
    const index = expandedMenus.value.indexOf(item.name)
    if (index > -1) {
      expandedMenus.value.splice(index, 1)
    } else {
      expandedMenus.value.push(item.name)
    }
  } else {
    activateMenu(item.name)
  }
}

function activateMenu(name) {
  emit('menu-click', name)
}

function reloadApp() {
  if (isReloading.value) return // 防止重复点击

  console.log('热重载Vue项目...')
  isReloading.value = true

  // 显示重载提示
  showReloadIndicator()

  // 延迟执行重载，让用户看到视觉反馈
  setTimeout(() => {
    // 重载当前页面但保持在同一个路由
    window.location.reload()
  }, 800)
}

function showReloadIndicator() {
  // 创建重载指示器
  const indicator = document.createElement('div')
  indicator.className = 'reload-indicator'
  indicator.innerHTML = `
    <div class="reload-content">
      <div class="reload-spinner">🔄</div>
      <div class="reload-text">正在重载Vue项目...</div>
    </div>
  `

  // 添加样式
  const style = document.createElement('style')
  style.textContent = `
    .reload-indicator {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(26, 26, 26, 0.95);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      backdrop-filter: blur(5px);
    }

    .reload-content {
      text-align: center;
      color: #ffffff;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .reload-spinner {
      font-size: 48px;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    .reload-text {
      font-size: 16px;
      font-weight: 500;
      color: #e0e0e0;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `

  document.head.appendChild(style)
  document.body.appendChild(indicator)
}

function updateTime() {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})

// Default emit
// emit('menu-click', activeMenu.value) --- REMOVED
</script>

<style scoped>
.sidebar-menu {
  width: 240px;
  background-color: #1a1a1a;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #333;
}

.menu-header {
  padding: 16px;
  border-bottom: 1px solid #333;
  background-color: #161616;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.app-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.menu-content {
  flex: 1;
  padding: 16px 10px;
  overflow-y: auto;
}

.menu-content::-webkit-scrollbar {
  width: 4px;
}

.menu-content::-webkit-scrollbar-track {
  background: transparent;
}

.menu-content::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 2px;
}

.menu-content::-webkit-scrollbar-thumb:hover {
  background: #444;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

li {
  margin-bottom: 4px;
  font-size: 15px;
  color: #e0e0e0;
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

li.active > .menu-item-content {
  background-color: #333;
  color: #ffffff;
}

li:not(.active) > .menu-item-content:hover {
  background-color: #2a2a2a;
}

li.has-children > .menu-item-content {
  justify-content: space-between;
}

.icon {
  margin-right: 12px;
  font-size: 18px;
}

.text {
  flex: 1;
  font-weight: 500;
}

.expand-icon {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.submenu {
  margin-top: 4px;
  margin-left: 20px;
  padding-left: 15px;
  border-left: 1px solid #333;
}

.submenu li {
  padding: 8px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.submenu li .icon {
  font-size: 16px;
}

.submenu li.active {
  background-color: #333;
  color: #ffffff;
}

.submenu li:not(.active):hover {
  background-color: #2a2a2a;
}

.menu-footer {
  padding: 16px;
  border-top: 1px solid #333;
}

.reload-section {
  margin-bottom: 12px;
}

.reload-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 12px;
  border: 1px solid #333;
  background-color: #2a2a2a;
  color: #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reload-btn:hover {
  background-color: #333;
  border-color: #444;
}

.reload-btn:disabled {
  background: linear-gradient(135deg, #666 0%, #555 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.reload-btn:disabled:hover {
  background: linear-gradient(135deg, #666 0%, #555 100%);
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.reload-icon {
  margin-right: 8px;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.reload-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.footer-info-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 4px;
  font-size: 12px;
  color: #a0a0a0;
  border-top: 1px solid #333;
}
</style>