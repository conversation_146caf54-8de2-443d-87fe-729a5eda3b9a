<template>
  <div class="workbench-container">
    <h1>工作台</h1>
    <p>这里是您的工作台，未来可以展示系统状态、快捷入口等信息。</p>
  </div>
</template>

<script setup>
// No script needed for this simple component yet
</script>

<style scoped>
.workbench-container {
  padding: 25px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #1a1a1a;
  color: #e0e0e0;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

h1 {
  font-size: 24px;
  color: #ffffff;
  margin-bottom: 15px;
}

p {
  font-size: 16px;
  color: #b0b0b0;
}
</style>