// 测试脚本：向数据库添加一些示例定时任务
const sqlite3 = require('sqlite3');
const { open } = require('sqlite');
const path = require('path');

async function addTestTasks() {
  try {
    // 连接到数据库
    const dbPath = path.join(__dirname, 'database.db');
    console.log('连接数据库:', dbPath);
    
    const db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });

    // 清空现有任务（可选）
    await db.run('DELETE FROM scheduled_tasks');
    console.log('已清空现有任务');

    // 添加示例任务
    const testTasks = [
      {
        name: '数据备份任务',
        task_type: 'cmd',
        script_name: 'backup.cmd',
        schedule_mode: 'repeat',
        cron_expression: '0 0 2 * * *',
        enabled: 1,
        status: 'idle'
      },
      {
        name: '系统监控脚本',
        task_type: 'python',
        script_name: 'monitor.py',
        schedule_mode: 'repeat',
        cron_expression: '0 */5 * * * *',
        enabled: 1,
        status: 'idle',
        last_run: new Date(Date.now() - 300000).toISOString()
      },
      {
        name: 'API健康检查',
        task_type: 'http',
        script_name: null,
        schedule_mode: 'repeat',
        cron_expression: '0 */1 * * * *',
        http_config: JSON.stringify({
          method: 'GET',
          url: 'https://api.example.com/health',
          headers: '{"Content-Type": "application/json"}',
          body: '{}'
        }),
        enabled: 0,
        status: 'idle'
      },
      {
        name: '日志清理任务',
        task_type: 'js',
        script_name: 'cleanup.js',
        schedule_mode: 'repeat',
        cron_expression: '0 0 0 * * 0',
        enabled: 1,
        status: 'idle'
      },
      {
        name: '一次性数据迁移',
        task_type: 'python',
        script_name: 'migrate.py',
        schedule_mode: 'once',
        execute_time: new Date(Date.now() + 86400000).toISOString(),
        enabled: 1,
        status: 'idle'
      }
    ];

    for (const task of testTasks) {
      const result = await db.run(`
        INSERT INTO scheduled_tasks (
          name, task_type, script_name, schedule_mode, cron_expression,
          execute_time, http_config, enabled, status, last_run,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [
        task.name,
        task.task_type,
        task.script_name,
        task.schedule_mode,
        task.cron_expression || null,
        task.execute_time || null,
        task.http_config || null,
        task.enabled,
        task.status,
        task.last_run || null
      ]);
      
      console.log(`已添加任务: ${task.name} (ID: ${result.lastID})`);
    }

    // 验证数据
    const tasks = await db.all('SELECT * FROM scheduled_tasks ORDER BY created_at DESC');
    console.log('\n当前数据库中的任务:');
    tasks.forEach(task => {
      console.log(`- ${task.name} (${task.task_type}) - ${task.enabled ? '启用' : '禁用'}`);
    });

    await db.close();
    console.log('\n测试数据添加完成！');
    
  } catch (error) {
    console.error('添加测试数据失败:', error);
  }
}

// 运行测试
addTestTasks();
